{"version": 3, "file": "image-name.js", "sourceRoot": "", "sources": ["../../src/container-runtime/image-name.ts"], "names": [], "mappings": ";;;AAAA,sCAAgC;AAEhC,MAAa,SAAS;IAMF;IACA;IACA;IAPF,MAAM,CAAS;IAEvB,MAAM,CAAU,KAAK,GAAG,iBAAiB,CAAC;IAElD,YACkB,QAA4B,EAC5B,KAAa,EACb,GAAW;QAFX,aAAQ,GAAR,QAAQ,CAAoB;QAC5B,UAAK,GAAL,KAAK,CAAQ;QACb,QAAG,GAAH,GAAG,CAAQ;QAE3B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE;YACtE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;YAEhE,+EAA+E;YAC/E,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ,GAAG,cAAc,IAAI,MAAM,CAAC;YAEzC,8EAA8E;YAC9E,IAAI,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClF,IAAI,WAAW,EAAE;gBACf,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAChD;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,KAAK,GAAG,GAAG,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAE3C,IAAI,OAAO,GAAG,6BAA6B,aAAa,aAAa,GAAG,oBAAoB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5G,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE;gBAChC,OAAO,IAAI,yBAAyB,IAAI,CAAC,KAAK,EAAE,CAAC;aAClD;YACD,YAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACnB;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAClC,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;aAC5D;iBAAM;gBACL,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;aAC5D;SACF;aAAM,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACpE,+DAA+D;YAC/D,uDAAuD;YACvD,qBAAqB;YACrB,EAAE;YACF,oFAAoF;YACpF,EAAE;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAC3C;IACH,CAAC;IAEM,MAAM,CAAC,KAAgB;QAC5B,OAAO,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC;IAClG,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,MAAc;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,qBAAqB,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAEvF,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtD,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;SAC5C;aAAM,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC9C,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtD,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;SAC5C;aAAM;YACL,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC;SACjE;IACH,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAAc;QACvC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACjD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;SACjB;IACH,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,MAAc;QACtC,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,WAAW,CAAC;IAChF,CAAC;;AAjFH,8BAkFC"}