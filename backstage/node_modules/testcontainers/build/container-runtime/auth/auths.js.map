{"version": 3, "file": "auths.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/auth/auths.ts"], "names": [], "mappings": ";;;AACA,yDAAqD;AAGrD,MAAa,KAAK;IACT,OAAO;QACZ,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,YAAoC;QAC/E,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,UAAU,GAAwB,EAAE,eAAe,EAAE,QAAQ,EAAE,CAAC;QAEtE,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChE,MAAM,CAAC,QAAQ,EAAE,GAAG,aAAa,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEzC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC/B,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAChC;aAAM;YACL,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;aACrC;SACF;QAED,OAAO,UAAwB,CAAC;IAClC,CAAC;IAEO,aAAa,CAAC,QAAgB,EAAE,YAAoC;QAC1E,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,IAAI,EAAE,CAAC;QAE7C,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;YAC7B,IAAI,IAAA,kCAAe,EAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;gBAClC,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;aACzB;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA/CD,sBA+CC"}