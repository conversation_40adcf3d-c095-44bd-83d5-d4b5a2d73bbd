{"version": 3, "file": "credential-provider.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/auth/credential-provider.ts"], "names": [], "mappings": ";;;AAAA,iDAA4C;AAC5C,yCAAmC;AAEnC,yDAAqD;AAQrD,MAAsB,kBAAkB;IAKtC,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,YAAoC;QACxE,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtF,IAAI,CAAC,sBAAsB,EAAE;YAC3B,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,WAAW,GAAG,qBAAqB,sBAAsB,EAAE,CAAC;QAClE,YAAG,CAAC,KAAK,CAAC,yCAAyC,WAAW,GAAG,CAAC,CAAC;QAEnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAE5D,MAAM,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAA,kCAAe,EAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,qBAAqB,EAAE;YAC1B,YAAG,CAAC,KAAK,CAAC,qCAAqC,QAAQ,GAAG,CAAC,CAAC;YAC5D,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAEzE,OAAO;YACL,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,MAAM;YACzB,eAAe,EAAE,QAAQ,CAAC,SAAS,IAAI,qBAAqB;SAC7D,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,YAAoB;QAC1C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAA,oBAAI,EAAC,GAAG,YAAY,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;gBACnD,IAAI,GAAG,EAAE;oBACP,IAAI,MAAM,KAAK,yBAAyB,EAAE;wBACxC,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;qBACpB;oBAED,YAAG,CAAC,KAAK,CAAC,0CAA0C,GAAG,EAAE,CAAC,CAAC;oBAC3D,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;iBACnE;gBACD,IAAI;oBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBACpC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;iBAC1B;gBAAC,OAAO,CAAC,EAAE;oBACV,YAAG,CAAC,KAAK,CAAC,sEAAsE,MAAM,GAAG,CAAC,CAAC;oBAC3F,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC,CAAC;iBAC9F;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,QAAgB,EAAE,YAAoB;QAClE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,IAAA,qBAAK,EAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAEtD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACxB,IAAI,IAAI,KAAK,CAAC,EAAE;oBACd,YAAG,CAAC,KAAK,CAAC,2CAA2C,IAAI,EAAE,CAAC,CAAC;oBAC7D,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC,CAAC;iBACpE;gBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjC,IAAI;oBACF,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAC5C,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC;iBAChC;gBAAC,OAAO,CAAC,EAAE;oBACV,YAAG,CAAC,KAAK,CAAC,qEAAqE,QAAQ,GAAG,CAAC,CAAC;oBAC5F,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC,CAAC;iBAC7F;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAhFD,gDAgFC"}