{"name": "@types/methods", "version": "1.1.4", "description": "TypeScript definitions for methods", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/methods", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "c<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cprecioso"}, {"name": "<PERSON>", "githubUsername": "mi<PERSON><PERSON>ter", "url": "https://github.com/michelbitter"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/methods"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "d007bd59e3ec325fa62010b866af352377e7827125cf63de9d4455392a34540d", "typeScriptVersion": "4.5"}