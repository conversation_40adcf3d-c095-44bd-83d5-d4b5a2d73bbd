# History

## v5.16.0 2023 April 29

-   Add `Dockerfile`, `dockerignore`, and `toml`
    -   Thanks to [<PERSON>](https://github.com/connorjchen) for [pull request #239](https://github.com/bevry/textextensions/pull/239)

## v5.15.0 2022 April 5

-   Add `xaml`
    -   Thanks to [<PERSON><PERSON>](https://github.com/SeriousBug) for [pull request #207](https://github.com/bevry/textextensions/pull/207)

## v5.14.0 2021 July 31

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.13.0 2021 July 28

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.12.0 2020 October 29

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.11.0 2020 September 5

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.10.0 2020 August 18

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.9.0 2020 August 4

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.8.0 2020 July 23

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.7.0 2020 June 25

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.6.0 2020 June 22

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.5.0 2020 June 21

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.4.0 2020 June 20

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.3.0 2020 June 11

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.2.0 2020 June 10

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v5.1.0 2020 June 1

-   Added RMarkdown support via `rmd` extension
    -   Thanks to [Brian Lukoff](https://github.com/brianlukoff) for [pull request #26](https://github.com/bevry/textextensions/pull/26)

## v5.0.0 2020 May 30

-   Breaking Change: If you are using CommonJS, you must now do `require('textextensions').default`
-   If you want a JSON file, it is available via `textextensions/list.json`, and CDN access is available via https://unpkg.com/textextensions/list.json
-   Converted to TypeScript
-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)
-   Minimum required node version changed back from `node: >=10` to `node: >=0.8` to support some users still on very old versions

## v4.4.0 2020 May 22

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v4.3.0 2020 May 21

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v4.2.0 2020 May 21

-   Fixed the busted v4.1.0 and v4.0.0 versions
-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v3.3.0 2019 December 10

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v3.2.0 2019 December 1

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v3.1.0 2019 December 1

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v3.0.0 2019 November 18

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)
-   Minimum required node version changed from `node: >=0.8` to `node: >=8` to keep up with mandatory ecosystem changes

## v2.6.0 2019 November 13

-   Updated dependencies, [base files](https://github.com/bevry/base), and [editions](https://editions.bevry.me) using [boundation](https://github.com/bevry/boundation)

## v2.5.0 2019 July 31

-   Add `njk` and `wxss`
    -   Thanks to [LvChengbin](https://github.com/LvChengbin) for [pull request #8](https://github.com/bevry/textextensions/pull/8)

## v2.4.0 2018 November 7

-   Add `wxml`
    -   Thanks to [刘祺](https://github.com/gucong3000) for [istextorbinary issue #9](https://github.com/bevry/istextorbinary/issues/9)

## v2.3.1 2018 November 7

-   readme updates

## v2.3.0 2018 November 7

-   Added a dozen or so new text extensions
    -   Thanks to [Joshua Evans](https://github.com/TheJoshuaEvans) for [pull request #6](https://github.com/bevry/textextensions/pull/6)
-   Updated [base files](https://github.com/bevry/base) and [editions](https://github.com/bevry/editions) using [boundation](https://github.com/bevry/boundation)

## v2.2.0 2018 January 25

-   Added a dozen or so new text extensions
    -   Thanks to [jaswrks](https://github.com/jaswrks) for [pull request #5](https://github.com/bevry/textextensions/pull/5)

## v2.1.0 2017 April 24

-   Added `noon` and `pug`
    -   Thanks to [monsterkodi](https://github.com/monsterkodi) for [pull request #4](https://github.com/bevry/textextensions/pull/4)

## v2.0.1 2016 May 10

-   JSON should have two space indentation

## v2.0.0 2016 May 10

-   Now a json file

## v1.1.0 2016 May 10

-   Added about 150 additional text extensions
    -   Thanks to [FelipeBB](https://github.com/FelipeBB) for [pull request #3](https://github.com/bevry/textextensions/pull/3)

## v1.0.2 2016 May 2

-   Updated meta files

## v1.0.1 2014 December 17

-   Added `yml` extension
    -   Thanks to [Jamy Timmermans](https://github.com/JamyDev) for [pull request #2](https://github.com/bevry/textextensions/pull/2)

## v1.0.0 2013 December 10

-   Extracted from [bal-util](https://github.com/balupton/bal-util/blob/6501d51bc0244fce3781fc0150136f7493099237/src/lib/paths.coffee#L48-L79)
