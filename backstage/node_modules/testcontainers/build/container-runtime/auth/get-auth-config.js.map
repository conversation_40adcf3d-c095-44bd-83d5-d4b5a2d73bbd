{"version": 3, "file": "get-auth-config.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/auth/get-auth-config.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAgC;AAChC,0CAAuC;AACvC,4CAAoB;AACpB,gDAAwB;AACxB,yCAAmC;AACnC,mCAAgC;AAChC,iDAA6C;AAC7C,+CAA2C;AAI3C,MAAM,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,GAAG,YAAE,CAAC,OAAO,EAAE,UAAU,CAAC;AAEpF,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;AAE3E,MAAM,gBAAgB,GAAG,KAAK,IAAqC,EAAE;IACnE,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;QAClC,OAAO,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;KAC1D;SAAM,IAAI,IAAA,eAAU,EAAC,gBAAgB,CAAC,EAAE;QACvC,OAAO,iBAAiB,CAAC,CAAC,MAAM,IAAA,mBAAQ,EAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;KACzE;SAAM;QACL,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;KAC5B;AACH,CAAC,CAAC;AAEF,SAAS,iBAAiB,CAAC,YAAoB;IAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAExC,OAAO;QACL,UAAU,EAAE,MAAM,CAAC,UAAU;QAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;KACpB,CAAC;AACJ,CAAC;AAED,MAAM,YAAY,GAAG,gBAAgB,EAAE,CAAC;AAExC,MAAM,oBAAoB,GAA0B,CAAC,IAAI,0BAAW,EAAE,EAAE,IAAI,wBAAU,EAAE,EAAE,IAAI,aAAK,EAAE,CAAC,CAAC;AAEvG,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkC,CAAC;AAEtD,MAAM,aAAa,GAAG,KAAK,EAAE,QAAgB,EAAmC,EAAE;IACvF,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC5B,YAAG,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,CAAC,CAAC;QAC9D,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KACjC;IAED,KAAK,MAAM,mBAAmB,IAAI,oBAAoB,EAAE;QACtD,MAAM,UAAU,GAAG,MAAM,mBAAmB,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,YAAY,CAAC,CAAC;QAEzF,IAAI,UAAU,EAAE;YACd,YAAG,CAAC,KAAK,CAAC,mCAAmC,QAAQ,MAAM,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC5F,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACrC,OAAO,UAAU,CAAC;SACnB;KACF;IAED,YAAG,CAAC,KAAK,CAAC,gDAAgD,QAAQ,GAAG,CAAC,CAAC;IACvE,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACpC,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAnBW,QAAA,aAAa,iBAmBxB"}