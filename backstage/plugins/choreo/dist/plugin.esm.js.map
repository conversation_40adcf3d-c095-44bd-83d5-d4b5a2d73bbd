{"version": 3, "file": "plugin.esm.js", "sources": ["../src/plugin.ts"], "sourcesContent": ["import {\n  createPlugin,\n  createRoutableExtension,\n} from '@backstage/core-plugin-api';\n\nimport { rootRouteRef } from './routes';\n\nexport const choreoPlugin = createPlugin({\n  id: 'choreo',\n  routes: {\n    root: rootRouteRef,\n  },\n});\n\nexport const ChoreoPage = choreoPlugin.provide(\n  createRoutableExtension({\n    name: 'ChoreoPage',\n    component: () =>\n      import('./components/ExampleComponent').then(m => m.ExampleComponent),\n    mountPoint: rootRouteRef,\n  }),\n);\n"], "names": [], "mappings": ";;;AAOO,MAAM,eAAe,YAAA,CAAa;AAAA,EACvC,EAAA,EAAI,QAAA;AAAA,EACJ,MAAA,EAAQ;AAAA,IACN,IAAA,EAAM;AAAA;AAEV,CAAC;AAEM,MAAM,aAAa,YAAA,CAAa,OAAA;AAAA,EACrC,uBAAA,CAAwB;AAAA,IACtB,IAAA,EAAM,YAAA;AAAA,IACN,SAAA,EAAW,MACT,OAAO,4CAA+B,EAAE,IAAA,CAAK,CAAA,CAAA,KAAK,EAAE,gBAAgB,CAAA;AAAA,IACtE,UAAA,EAAY;AAAA,GACb;AACH;;;;"}