import { createPlugin, createRoutableExtension } from '@backstage/core-plugin-api';
import { rootRouteRef } from './routes.esm.js';

const choreoPlugin = createPlugin({
  id: "choreo",
  routes: {
    root: rootRouteRef
  }
});
const ChoreoPage = choreoPlugin.provide(
  createRoutableExtension({
    name: "ChoreoPage",
    component: () => import('./components/ExampleComponent/index.esm.js').then((m) => m.ExampleComponent),
    mountPoint: rootRouteRef
  })
);

export { ChoreoPage, choreoPlugin };
//# sourceMappingURL=plugin.esm.js.map
