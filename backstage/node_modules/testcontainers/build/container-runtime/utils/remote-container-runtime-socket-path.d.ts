/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { ContainerRuntimeClientStrategyResult } from "../strategies/types";
export declare const getRemoteContainerRuntimeSocketPath: (containerRuntimeStrategyResult: ContainerRuntimeClientStrategyResult, containerRuntimeOs: string, platform?: NodeJS.Platform, env?: NodeJS.ProcessEnv) => string;
