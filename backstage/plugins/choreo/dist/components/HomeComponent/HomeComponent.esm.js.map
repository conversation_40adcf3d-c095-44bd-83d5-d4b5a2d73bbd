{"version": 3, "file": "HomeComponent.esm.js", "sources": ["../../../src/components/HomeComponent/HomeComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Typography,\n  Grid,\n  makeStyles,\n  Theme,\n} from '@material-ui/core';\nimport {\n  InfoCard,\n  Page,\n  Content,\n  ContentHeader,\n  Header,\n} from '@backstage/core-components';\nimport { useNavigate } from 'react-router-dom';\n\nconst useStyles = makeStyles((theme: Theme) => ({\n  root: {\n    backgroundColor: theme.palette.background.default,\n  },\n  welcomeCard: {\n    padding: theme.spacing(3),\n  },\n  welcomeText: {\n    marginBottom: theme.spacing(2),\n  },\n  placeholderText: {\n    color: theme.palette.text.secondary,\n    fontStyle: 'italic',\n  },\n}));\n\nexport interface HomeComponentProps {\n  /** Optional custom title for the page */\n  title?: string;\n  /** Optional custom subtitle for the page */\n  subtitle?: string;\n}\n\nexport const HomeComponent: React.FC<HomeComponentProps> = ({\n  title = 'Welcome to Choreo',\n  subtitle = 'Your development platform dashboard',\n}) => {\n  const classes = useStyles();\n  const navigate = useNavigate();\n\n  const handleBackToSignIn = () => {\n    navigate('/choreo-signin');\n  };\n\n  return (\n    <Page themeId=\"home\" className={classes.root}>\n      <Header title={title} subtitle={subtitle} />\n      <Content>\n        <ContentHeader title=\"Dashboard\">\n          <Typography variant=\"body1\" color=\"textSecondary\">\n            Welcome to your Choreo dashboard. This is where you'll manage your projects and services.\n          </Typography>\n        </ContentHeader>\n        <Grid container spacing={3} direction=\"column\">\n          <Grid item>\n            <InfoCard title=\"Getting Started\" className={classes.welcomeCard}>\n              <Typography variant=\"body1\" className={classes.welcomeText}>\n                Welcome to Choreo! You have successfully signed in to your account.\n              </Typography>\n              <Typography variant=\"body2\" className={classes.placeholderText}>\n                This is a placeholder home page. Future features will include:\n              </Typography>\n              <ul>\n                <li>\n                  <Typography variant=\"body2\" className={classes.placeholderText}>\n                    Project management dashboard\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\" className={classes.placeholderText}>\n                    Service deployment status\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\" className={classes.placeholderText}>\n                    Analytics and monitoring\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\" className={classes.placeholderText}>\n                    Team collaboration tools\n                  </Typography>\n                </li>\n              </ul>\n            </InfoCard>\n          </Grid>\n          <Grid item>\n            <InfoCard title=\"Quick Actions\">\n              <Typography variant=\"body2\" className={classes.placeholderText}>\n                Quick action buttons and shortcuts will be available here.\n              </Typography>\n            </InfoCard>\n          </Grid>\n        </Grid>\n      </Content>\n    </Page>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAgBA,MAAM,SAAA,GAAY,UAAA,CAAW,CAAC,KAAA,MAAkB;AAAA,EAC9C,IAAA,EAAM;AAAA,IACJ,eAAA,EAAiB,KAAA,CAAM,OAAA,CAAQ,UAAA,CAAW;AAAA,GAC5C;AAAA,EACA,WAAA,EAAa;AAAA,IACX,OAAA,EAAS,KAAA,CAAM,OAAA,CAAQ,CAAC;AAAA,GAC1B;AAAA,EACA,WAAA,EAAa;AAAA,IACX,YAAA,EAAc,KAAA,CAAM,OAAA,CAAQ,CAAC;AAAA,GAC/B;AAAA,EACA,eAAA,EAAiB;AAAA,IACf,KAAA,EAAO,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,SAAA;AAAA,IAC1B,SAAA,EAAW;AAAA;AAEf,CAAA,CAAE,CAAA;AASK,MAAM,gBAA8C,CAAC;AAAA,EAC1D,KAAA,GAAQ,mBAAA;AAAA,EACR,QAAA,GAAW;AACb,CAAA,KAAM;AACJ,EAAA,MAAM,UAAU,SAAA,EAAU;AAC1B,EAAiB,WAAA;AAMjB,EAAA,4BACG,IAAA,EAAA,EAAK,OAAA,EAAQ,MAAA,EAAO,SAAA,EAAW,QAAQ,IAAA,EACtC,QAAA,EAAA;AAAA,oBAAA,GAAA,CAAC,MAAA,EAAA,EAAO,OAAc,QAAA,EAAoB,CAAA;AAAA,yBACzC,OAAA,EAAA,EACC,QAAA,EAAA;AAAA,sBAAA,GAAA,CAAC,aAAA,EAAA,EAAc,KAAA,EAAM,WAAA,EACnB,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,SAAQ,OAAA,EAAQ,KAAA,EAAM,eAAA,EAAgB,QAAA,EAAA,2FAAA,EAElD,CAAA,EACF,CAAA;AAAA,2BACC,IAAA,EAAA,EAAK,SAAA,EAAS,MAAC,OAAA,EAAS,CAAA,EAAG,WAAU,QAAA,EACpC,QAAA,EAAA;AAAA,wBAAA,GAAA,CAAC,IAAA,EAAA,EAAK,MAAI,IAAA,EACR,QAAA,kBAAA,IAAA,CAAC,YAAS,KAAA,EAAM,iBAAA,EAAkB,SAAA,EAAW,OAAA,CAAQ,WAAA,EACnD,QAAA,EAAA;AAAA,0BAAA,GAAA,CAAC,cAAW,OAAA,EAAQ,OAAA,EAAQ,SAAA,EAAW,OAAA,CAAQ,aAAa,QAAA,EAAA,qEAAA,EAE5D,CAAA;AAAA,8BACC,UAAA,EAAA,EAAW,OAAA,EAAQ,SAAQ,SAAA,EAAW,OAAA,CAAQ,iBAAiB,QAAA,EAAA,gEAAA,EAEhE,CAAA;AAAA,+BACC,IAAA,EAAA,EACC,QAAA,EAAA;AAAA,4BAAA,GAAA,CAAC,IAAA,EAAA,EACC,8BAAC,UAAA,EAAA,EAAW,OAAA,EAAQ,SAAQ,SAAA,EAAW,OAAA,CAAQ,eAAA,EAAiB,QAAA,EAAA,8BAAA,EAEhE,CAAA,EACF,CAAA;AAAA,4BACA,GAAA,CAAC,IAAA,EAAA,EACC,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,OAAA,EAAQ,SAAQ,SAAA,EAAW,OAAA,CAAQ,eAAA,EAAiB,QAAA,EAAA,2BAAA,EAEhE,CAAA,EACF,CAAA;AAAA,4BACA,GAAA,CAAC,IAAA,EAAA,EACC,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,OAAA,EAAQ,SAAQ,SAAA,EAAW,OAAA,CAAQ,eAAA,EAAiB,QAAA,EAAA,0BAAA,EAEhE,CAAA,EACF,CAAA;AAAA,4BACA,GAAA,CAAC,IAAA,EAAA,EACC,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,OAAA,EAAQ,SAAQ,SAAA,EAAW,OAAA,CAAQ,eAAA,EAAiB,QAAA,EAAA,0BAAA,EAEhE,CAAA,EACF;AAAA,WAAA,EACF;AAAA,SAAA,EACF,CAAA,EACF,CAAA;AAAA,4BACC,IAAA,EAAA,EAAK,IAAA,EAAI,IAAA,EACR,QAAA,kBAAA,GAAA,CAAC,YAAS,KAAA,EAAM,eAAA,EACd,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,SAAQ,OAAA,EAAQ,SAAA,EAAW,QAAQ,eAAA,EAAiB,QAAA,EAAA,4DAAA,EAEhE,GACF,CAAA,EACF;AAAA,OAAA,EACF;AAAA,KAAA,EACF;AAAA,GAAA,EACF,CAAA;AAEJ;;;;"}