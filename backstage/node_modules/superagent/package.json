{"name": "superagent", "description": "elegant & feature rich browser / node HTTP with a fluent API", "version": "8.1.2", "author": "<PERSON><PERSON> <<EMAIL>>", "browser": {"./src/node/index.js": "./src/client.js", "./lib/node/index.js": "./lib/client.js", "./test/support/server.js": "./test/support/blank.js", "semver": false}, "bugs": {"url": "https://github.com/ladjs/superagent/issues"}, "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Loftis <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.4", "debug": "^4.3.4", "fast-safe-stringify": "^2.1.1", "form-data": "^4.0.0", "formidable": "^2.1.2", "methods": "^1.1.2", "mime": "2.6.0", "qs": "^6.11.0", "semver": "^7.3.8"}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.12", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/runtime": "^7.20.13", "@commitlint/cli": "17", "@commitlint/config-conventional": "17", "Base64": "^1.1.0", "babelify": "^10.0.0", "basic-auth-connect": "^1.0.0", "body-parser": "^1.20.1", "browserify": "^17.0.0", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "eslint": "^8.32.0", "eslint-config-xo-lass": "2", "eslint-plugin-compat": "4.0.2", "eslint-plugin-node": "^11.1.0", "express": "^4.18.2", "express-session": "^1.17.3", "fixpack": "^4.0.0", "get-port": "4.2.0", "husky": "7", "lint-staged": "12", "marked": "^4.2.12", "mocha": "^6.2.3", "multer": "1.4.5-lts.1", "nyc": "^15.1.0", "remark-cli": "^11.0.0", "remark-preset-github": "4.0.4", "rimraf": "3", "should": "^13.2.3", "should-http": "^0.1.1", "tinyify": "3.0.0", "xo": "^0.53.1", "zuul": "^3.12.0"}, "engines": {"node": ">=6.4.0 <13 || >=14"}, "files": ["dist/*.js", "lib/**/*.js"], "homepage": "https://github.com/ladjs/superagent", "jsdelivr": "dist/superagent.min.js", "keywords": ["agent", "ajax", "ajax", "api", "async", "await", "axios", "cancel", "client", "frisbee", "got", "http", "http", "https", "ky", "promise", "promise", "promises", "request", "request", "requests", "response", "rest", "retry", "super", "superagent", "timeout", "transform", "xhr", "xmlhttprequest"], "license": "MIT", "main": "lib/node/index.js", "repository": {"type": "git", "url": "git://github.com/ladjs/superagent.git"}, "scripts": {"browserify": "browserify src/node/index.js -o dist/superagent.js -s superagent -g [ babelify --configFile ./.dist.babelrc ]", "build": "npm run build:clean && npm run build:lib && npm run build:dist", "build:clean": "<PERSON><PERSON><PERSON> lib dist", "build:dist": "npm run browserify && npm run minify", "build:lib": "babel --config-file ./.lib.babelrc src --out-dir lib", "build:test": "babel --config-file ./.test.babelrc test --out-dir lib/node/test", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint": "eslint -c .eslintrc src test && remark . -qfo && eslint -c .lib.eslintrc lib/**/*.js && eslint -c .dist.eslintrc dist/**/*.js", "minify": "cross-env NODE_ENV=production browserify src/node/index.js -o dist/superagent.min.js -s superagent -g [ babelify --configFile ./.dist.babelrc ] -p tinyify", "nyc": "cross-env NODE_ENV=test nyc ava", "prepare": "husky install", "test": "npm run build && npm run lint && make test", "test-http2": "npm run build && npm run lint && make test-node-http2"}, "unpkg": "dist/superagent.min.js"}