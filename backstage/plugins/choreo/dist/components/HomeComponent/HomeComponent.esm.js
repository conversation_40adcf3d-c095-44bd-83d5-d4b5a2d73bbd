import { jsxs, jsx } from 'react/jsx-runtime';
import { makeStyles, Typography, Grid } from '@material-ui/core';
import { Page, Header, Content, ContentHeader, InfoCard } from '@backstage/core-components';
import { useNavigate } from 'react-router-dom';

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: theme.palette.background.default
  },
  welcomeCard: {
    padding: theme.spacing(3)
  },
  welcomeText: {
    marginBottom: theme.spacing(2)
  },
  placeholderText: {
    color: theme.palette.text.secondary,
    fontStyle: "italic"
  }
}));
const HomeComponent = ({
  title = "Welcome to Choreo",
  subtitle = "Your development platform dashboard"
}) => {
  const classes = useStyles();
  useNavigate();
  return /* @__PURE__ */ jsxs(Page, { themeId: "home", className: classes.root, children: [
    /* @__PURE__ */ jsx(<PERSON><PERSON>, { title, subtitle }),
    /* @__PURE__ */ jsxs(Content, { children: [
      /* @__PURE__ */ jsx(ContentHeader, { title: "Dashboard", children: /* @__PURE__ */ jsx(Typography, { variant: "body1", color: "textSecondary", children: "Welcome to your Choreo dashboard. This is where you'll manage your projects and services." }) }),
      /* @__PURE__ */ jsxs(Grid, { container: true, spacing: 3, direction: "column", children: [
        /* @__PURE__ */ jsx(Grid, { item: true, children: /* @__PURE__ */ jsxs(InfoCard, { title: "Getting Started", className: classes.welcomeCard, children: [
          /* @__PURE__ */ jsx(Typography, { variant: "body1", className: classes.welcomeText, children: "Welcome to Choreo! You have successfully signed in to your account." }),
          /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "This is a placeholder home page. Future features will include:" }),
          /* @__PURE__ */ jsxs("ul", { children: [
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "Project management dashboard" }) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "Service deployment status" }) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "Analytics and monitoring" }) }),
            /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "Team collaboration tools" }) })
          ] })
        ] }) }),
        /* @__PURE__ */ jsx(Grid, { item: true, children: /* @__PURE__ */ jsx(InfoCard, { title: "Quick Actions", children: /* @__PURE__ */ jsx(Typography, { variant: "body2", className: classes.placeholderText, children: "Quick action buttons and shortcuts will be available here." }) }) })
      ] })
    ] })
  ] });
};

export { HomeComponent };
//# sourceMappingURL=HomeComponent.esm.js.map
