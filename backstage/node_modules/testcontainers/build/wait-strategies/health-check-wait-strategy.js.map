{"version": 3, "file": "health-check-wait-strategy.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/health-check-wait-strategy.ts"], "names": [], "mappings": ";;;AACA,sCAA+C;AAC/C,4DAAiE;AACjE,mDAAuD;AAEvD,MAAa,uBAAwB,SAAQ,oCAAoB;IACxD,KAAK,CAAC,cAAc,CAAC,SAA8B;QACxD,YAAG,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QAEjD,MAAM,MAAM,GAAG,MAAM,IAAI,sBAAa,CAA4B,GAAG,CAAC,CAAC,UAAU,CAC/E,KAAK,IAAI,EAAE,CAAC,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAC5E,CAAC,iBAAiB,EAAE,EAAE,CAAC,iBAAiB,KAAK,SAAS,IAAI,iBAAiB,KAAK,WAAW,EAC3F,GAAG,EAAE;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;YACpC,MAAM,OAAO,GAAG,kCAAkC,OAAO,IAAI,CAAC;YAC9D,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC,EACD,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,MAAM,OAAO,GAAG,wBAAwB,MAAM,EAAE,CAAC;YACjD,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QAED,YAAG,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAClF,CAAC;CACF;AAzBD,0DAyBC"}