{"version": 3, "file": "composite-wait-strategy.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/composite-wait-strategy.ts"], "names": [], "mappings": ";;;AACA,sCAAgC;AAEhC,mDAAqE;AAErE,MAAa,qBAAsB,SAAQ,oCAAoB;IAGhC;IAFrB,QAAQ,CAAU;IAE1B,YAA6B,cAA8B;QACzD,KAAK,EAAE,CAAC;QADmB,mBAAc,GAAd,cAAc,CAAgB;IAE3D,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAA8B,EAAE,UAAsB,EAAE,SAAgB;QAClG,YAAG,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAErE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,eAA+B,CAAC;YACpC,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC/B,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;oBAChC,MAAM,OAAO,GAAG,gDAAgD,IAAI,CAAC,QAAQ,IAAI,CAAC;oBAClF,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;oBAClD,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC7B,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACnB;YAED,OAAO,CAAC,GAAG,CACT,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CACzG;iBACE,IAAI,CAAC,GAAG,EAAE;gBACT,YAAG,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC7E,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBAC3B,OAAO,CAAC,GAAG,EAAE;gBACZ,IAAI,eAAe,EAAE;oBACnB,YAAY,CAAC,eAAe,CAAC,CAAC;iBAC/B;YACH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEe,kBAAkB,CAAC,cAAsB;QACvD,IAAI,CAAC,cAAc;aAChB,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;aAC7D,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,YAAY,CAAC,QAAgB;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA/CD,sDA+CC"}