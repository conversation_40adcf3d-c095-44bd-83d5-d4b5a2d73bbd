type Method =
    | "ACL"
    | "BIND"
    | "CHECKOUT"
    | "CONNECT"
    | "COPY"
    | "DELETE"
    | "GET"
    | "HEAD"
    | "LINK"
    | "LOCK"
    | "M-SEARCH"
    | "MERGE"
    | "MKACTIVITY"
    | "MKCALENDAR"
    | "MKCOL"
    | "MOVE"
    | "NOTIFY"
    | "OPTIONS"
    | "PATCH"
    | "POST"
    | "PROPFIND"
    | "PROPPATCH"
    | "PURGE"
    | "PUT"
    | "REBIND"
    | "REPORT"
    | "SEARCH"
    | "SOURCE"
    | "SUBSCRIBE"
    | "TRACE"
    | "UNBIND"
    | "UNLINK"
    | "UNLOCK"
    | "UNSUBSCRIBE"
    | "acl"
    | "bind"
    | "checkout"
    | "connect"
    | "copy"
    | "delete"
    | "get"
    | "head"
    | "link"
    | "lock"
    | "m-search"
    | "merge"
    | "mkactivity"
    | "mkcalendar"
    | "mkcol"
    | "move"
    | "notify"
    | "options"
    | "patch"
    | "post"
    | "propfind"
    | "proppatch"
    | "purge"
    | "put"
    | "rebind"
    | "report"
    | "search"
    | "source"
    | "subscribe"
    | "trace"
    | "unbind"
    | "unlink"
    | "unlock"
    | "unsubscribe";

declare const methods: Method[];
export = methods;
