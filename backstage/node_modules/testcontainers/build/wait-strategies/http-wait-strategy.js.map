{"version": 3, "file": "http-wait-strategy.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/http-wait-strategy.ts"], "names": [], "mappings": ";;;AACA,mCAA+B;AAC/B,sCAA+C;AAC/C,4DAAiE;AAEjE,mDAAuD;AAMvD,MAAa,gBAAiB,SAAQ,oCAAoB;IASrC;IACA;IACA;IAVX,QAAQ,GAAG,MAAM,CAAC;IAClB,MAAM,GAAG,KAAK,CAAC;IACf,OAAO,GAA8B,EAAE,CAAC;IACxC,UAAU,GAAoD,EAAE,CAAC;IACjE,cAAc,GAAG,KAAK,CAAC;IACvB,WAAW,GAAG,IAAI,CAAC;IAE3B,YACmB,IAAY,EACZ,IAAY,EACZ,OAAgC;QAEjD,KAAK,EAAE,CAAC;QAJS,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAQ;QACZ,YAAO,GAAP,OAAO,CAAyB;IAGnD,CAAC;IAEM,aAAa,CAAC,UAAkB;QACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,QAAkB,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,qBAAqB,CAAC,SAA0C;QACrE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,QAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,oBAAoB,CAAC,SAAwC;QAClE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,QAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,UAAU,CAAC,MAAc;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,WAAW,CAAC,OAAkC;QACnD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,oBAAoB,CAAC,QAAgB,EAAE,QAAgB;QAC5D,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChF,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,aAAa,EAAE,EAAE,CAAC;QAC5E,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CAAC,WAAmB;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,QAAQ;QACb,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,aAAa;QAClB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAA8B,EAAE,UAAsB;QAChF,YAAG,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhE,MAAM,UAAU,GAAG,QAAQ,CAAC;QAC5B,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE9C,MAAM,IAAI,sBAAa,CAA8B,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,CAC/E,KAAK,IAAI,EAAE;YACT,IAAI;gBACF,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GACrG,IAAI,CAAC,IACP,EAAE,CAAC;gBAEH,IAAI,oBAAoB,EAAE;oBACxB,MAAM,eAAe,GAAG,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;oBAEjF,IAAI,eAAe,KAAK,UAAU,EAAE;wBAClC,eAAe,GAAG,IAAI,CAAC;wBAEvB,OAAO;qBACR;iBACF;gBAED,OAAO,MAAM,KAAK,CAAC,GAAG,EAAE;oBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;oBAC7C,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE;iBAC5B,CAAC,CAAC;aACJ;YAAC,MAAM;gBACN,OAAO,SAAS,CAAC;aAClB;QACH,CAAC,EACD,KAAK,EAAE,QAAQ,EAAE,EAAE;YACjB,IAAI,oBAAoB,IAAI,eAAe,EAAE;gBAC3C,OAAO,IAAI,CAAC;aACb;YAED,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,OAAO,KAAK,CAAC;aACd;iBAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBAClC,OAAO,QAAQ,CAAC,EAAE,CAAC;aACpB;iBAAM;gBACL,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;oBACvC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,CAAC;oBACzC,IAAI,CAAC,MAAM,EAAE;wBACX,OAAO,KAAK,CAAC;qBACd;iBACF;gBACD,OAAO,IAAI,CAAC;aACb;QACH,CAAC,EACD,GAAG,EAAE;YACH,MAAM,OAAO,GAAG,OAAO,IAAI,CAAC,IAAI,yBAAyB,IAAI,CAAC,cAAc,IAAI,CAAC;YACjF,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC,EACD,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,IAAI,oBAAoB,IAAI,eAAe,EAAE;YAC3C,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;SAC5C;QAED,YAAG,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAA8B;QAC9D,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,IAAI,OAAe,CAAC;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhE,MAAM,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBACxB,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;YAEH,OAAO,GAAG,kDAAkD,IAAI,UAAU,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;SACjG;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,GAAG,mEAAmE,CAAC;SAC/E;QAED,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAElD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,OAAO,IAAI,cAAK,CAAC;gBACf,OAAO,EAAE;oBACP,kBAAkB,EAAE,KAAK;iBAC1B;aACF,CAAC,CAAC;SACJ;IACH,CAAC;CACF;AAnKD,4CAmKC"}