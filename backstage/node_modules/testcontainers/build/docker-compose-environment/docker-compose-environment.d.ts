import { Uuid } from "../common";
import { Environment } from "../types";
import { ImagePullPolicy } from "../utils/pull-policy";
import { WaitStrategy } from "../wait-strategies/wait-strategy";
import { StartedDockerComposeEnvironment } from "./started-docker-compose-environment";
export declare class DockerComposeEnvironment {
    private readonly composeFilePath;
    private readonly composeFiles;
    private projectName;
    private build;
    private recreate;
    private environmentFile;
    private profiles;
    private environment;
    private pullPolicy;
    private defaultWaitStrategy;
    private waitStrategy;
    private startupTimeout?;
    constructor(composeFilePath: string, composeFiles: string | string[], uuid?: Uuid);
    withBuild(): this;
    withEnvironment(environment: Environment): this;
    withEnvironmentFile(environmentFile: string): this;
    withProfiles(...profiles: string[]): this;
    withNoRecreate(): this;
    withPullPolicy(pullPolicy: ImagePullPolicy): this;
    withDefaultWaitStrategy(waitStrategy: WaitStrategy): this;
    withWaitStrategy(containerName: string, waitStrategy: WaitStrategy): this;
    withStartupTimeout(startupTimeout: number): this;
    withProjectName(projectName: string): this;
    up(services?: Array<string>): Promise<StartedDockerComposeEnvironment>;
}
