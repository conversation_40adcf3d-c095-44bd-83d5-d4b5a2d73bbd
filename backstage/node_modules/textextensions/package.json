{"title": "The Definitive List of Text Extensions", "name": "textextensions", "version": "5.16.0", "description": "A package that contains an array of every single file extension there is for text files", "homepage": "https://github.com/bevry/textextensions", "license": "MIT", "keywords": ["browser", "es5", "export-default", "extensions", "module", "node", "text", "text extensions", "text-extensions", "typed", "types", "typescript"], "badges": {"list": ["githubworkflow", "npmversion", "npmdownloads", "da<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dm<PERSON>v", "---", "githubsponsors", "patreon", "flattr", "liberapay", "buymeacoffee", "opencollective", "crypto", "paypal", "wishlist"], "config": {"githubWorkflow": "bevry", "githubSponsorsUsername": "bal<PERSON><PERSON>", "buymeacoffeeUsername": "bal<PERSON><PERSON>", "cryptoURL": "https://bevry.me/crypto", "flattrUsername": "bal<PERSON><PERSON>", "liberapayUsername": "bevry", "opencollectiveUsername": "bevry", "patreonUsername": "bevry", "paypalURL": "https://bevry.me/paypal", "wishlistURL": "https://bevry.me/wishlist", "githubUsername": "bevry", "githubRepository": "textextensions", "githubSlug": "bevry/textextensions", "npmPackageName": "textextensions"}}, "funding": "https://bevry.me/fund", "author": "2013+ Bevry Pty Ltd <<EMAIL>> (http://bevry.me)", "maintainers": ["<PERSON> <<EMAIL>> (https://github.com/balupton)"], "contributors": ["<PERSON> <<EMAIL>> (https://github.com/balupton)", "<PERSON> (https://github.com/brian<PERSON><PERSON>)", "<PERSON> <<EMAIL>> (https://github.com/fbeline)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/JamyDev)", "<PERSON> (https://github.com/jaswrks)", "<PERSON> <<EMAIL>> (https://github.com/TheJoshuaEvans)", "LvChengbin <<EMAIL>> (https://github.com/LvChengbin)", "monsterkodi (https://github.com/monsterkodi)"], "bugs": {"url": "https://github.com/bevry/textextensions/issues"}, "repository": {"type": "git", "url": "https://github.com/bevry/textextensions.git"}, "engines": {"node": ">=0.8"}, "editions": [{"description": "TypeScript source code with Import for modules", "directory": "source", "entry": "index.ts", "tags": ["source", "typescript", "import"], "engines": false}, {"description": "TypeScript compiled against ES2020 for web browsers with Import for modules", "directory": "edition-browsers", "entry": "index.js", "tags": ["compiled", "javascript", "import"], "engines": {"node": false, "browsers": "defaults"}}, {"description": "TypeScript compiled against ES5 for Node.js with Require for modules", "directory": "edition-es5", "entry": "index.js", "tags": ["compiled", "javascript", "es5", "require"], "engines": {"node": "10 || 12 || 14 || 16", "browsers": false}}, {"description": "TypeScript compiled against ES5 for Node.js with Import for modules", "directory": "edition-es5-esm", "entry": "index.js", "tags": ["compiled", "javascript", "es5", "import"], "engines": {"node": "12 || 14 || 16", "browsers": false}}], "types": "./compiled-types/", "type": "module", "main": "edition-es5/index.js", "exports": {"node": {"import": "./edition-es5-esm/index.js", "require": "./edition-es5/index.js"}, "browser": {"import": "./edition-browsers/index.js"}}, "browser": "edition-browsers/index.js", "module": "edition-browsers/index.js", "devDependencies": {"@bevry/update-contributors": "^1.19.0", "@typescript-eslint/eslint-plugin": "^4.28.5", "@typescript-eslint/parser": "^4.28.5", "assert-helpers": "^8.4.0", "binaryextensions": "^4.18.0", "eslint": "^7.31.0", "eslint-config-bevry": "^3.27.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "filedirname": "^2.7.0", "kava": "^5.15.0", "make-deno-edition": "^1.3.0", "prettier": "^2.3.2", "projectz": "^2.22.0", "surge": "^0.23.0", "typedoc": "^0.21.4", "typescript": "4.3.5", "valid-directory": "^3.7.0", "valid-module": "^1.16.0"}, "scripts": {"our:clean": "rm -Rf ./docs ./edition* ./es2015 ./es5 ./out ./.next", "our:compile": "npm run our:compile:deno && npm run our:compile:edition-browsers && npm run our:compile:edition-es5 && npm run our:compile:edition-es5-esm && npm run our:compile:types", "our:compile:deno": "make-deno-edition --attempt", "our:compile:edition-browsers": "tsc --module ESNext --target ES2020 --outDir ./edition-browsers --project tsconfig.json && ( test ! -d edition-browsers/source || ( mv edition-browsers/source edition-temp && rm -Rf edition-browsers && mv edition-temp edition-browsers ) )", "our:compile:edition-es5": "tsc --module commonjs --target ES5 --outDir ./edition-es5 --project tsconfig.json && ( test ! -d edition-es5/source || ( mv edition-es5/source edition-temp && rm -Rf edition-es5 && mv edition-temp edition-es5 ) ) && echo '{\"type\": \"commonjs\"}' > edition-es5/package.json", "our:compile:edition-es5-esm": "tsc --module ESNext --target ES5 --outDir ./edition-es5-esm --project tsconfig.json && ( test ! -d edition-es5-esm/source || ( mv edition-es5-esm/source edition-temp && rm -Rf edition-es5-esm && mv edition-temp edition-es5-esm ) ) && echo '{\"type\": \"module\"}' > edition-es5-esm/package.json", "our:compile:types": "tsc --project tsconfig.json --emitDeclarationOnly --declaration --declarationMap --declarationDir ./compiled-types && ( test ! -d compiled-types/source || ( mv compiled-types/source edition-temp && rm -Rf compiled-types && mv edition-temp compiled-types ) )", "our:deploy": "echo no need for this project", "our:meta": "npm run our:meta:contributors && npm run our:meta:docs && npm run our:meta:projectz", "our:meta:contributors": "update-contributors", "our:meta:docs": "npm run our:meta:docs:typedoc", "our:meta:docs:typedoc": "rm -Rf ./docs && typedoc --exclude '**/+(*test*|node_modules)' --excludeExternals --out ./docs ./source", "our:meta:projectz": "projectz compile", "our:release": "npm run our:release:prepare && npm run our:release:check-changelog && npm run our:release:check-dirty && npm run our:release:tag && npm run our:release:push", "our:release:check-changelog": "cat ./HISTORY.md | grep v$npm_package_version || (echo add a changelog entry for v$npm_package_version && exit -1)", "our:release:check-dirty": "git diff --exit-code", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:push": "git push origin && git push origin --tags", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (echo 'proper changelog entry not found' && exit -1) && git tag v$npm_package_version -am \"$MESSAGE\"", "our:setup": "npm run our:setup:install", "our:setup:install": "npm install", "our:test": "npm run our:verify && npm test", "our:verify": "npm run our:verify:directory && npm run our:verify:eslint && npm run our:verify:module && npm run our:verify:prettier", "our:verify:directory": "valid-directory", "our:verify:eslint": "eslint --fix --ignore-pattern '**/*.d.ts' --ignore-pattern '**/vendor/' --ignore-pattern '**/node_modules/' --ext .mjs,.js,.jsx,.ts,.tsx ./source", "our:verify:module": "valid-module", "our:verify:prettier": "prettier --write .", "test": "node ./edition-es5/test.js"}, "eslintConfig": {"extends": ["bevry"]}, "prettier": {"semi": false, "singleQuote": true}, "boundation": {"nodeVersionsTestedRange": ">=10", "compiler": "typescript", "targets": ["ES5"]}}