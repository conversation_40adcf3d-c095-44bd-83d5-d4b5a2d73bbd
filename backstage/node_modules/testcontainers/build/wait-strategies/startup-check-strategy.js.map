{"version": 3, "file": "startup-check-strategy.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/startup-check-strategy.ts"], "names": [], "mappings": ";;;AACA,sCAA+C;AAC/C,4DAAiE;AACjE,mDAAuD;AAIvD,MAAsB,oBAAqB,SAAQ,oCAAoB;IACrE;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAIe,KAAK,CAAC,cAAc,CAAC,SAA8B;QACjE,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QAEjD,MAAM,aAAa,GAAG,MAAM,IAAI,sBAAa,CAAuB,IAAI,CAAC,CAAC,UAAU,CAClF,KAAK,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,EAClF,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,MAAM,EAC1E,GAAG,EAAE;YACH,MAAM,OAAO,GAAG,kCAAkC,IAAI,CAAC,cAAc,IAAI,CAAC;YAC1E,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC,EACD,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,IAAI,aAAa,YAAY,KAAK,EAAE;YAClC,MAAM,aAAa,CAAC;SACrB;aAAM,IAAI,aAAa,KAAK,MAAM,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,iCAAiC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;SAClE;IACH,CAAC;CACF;AA3BD,oDA2BC"}