{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/common/logger.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAyC;AAOzC,MAAa,MAAM;IAKE;IAJF,MAAM,CAAY;IAEnC,YACE,SAAiB,EACA,YAAY,IAAI;QAAhB,cAAS,GAAT,SAAS,CAAO;QAEjC,IAAI,CAAC,MAAM,GAAG,IAAA,eAAK,EAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;IACH,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,OAAe,EAAE,OAAiB;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEM,KAAK,CAAC,OAAe,EAAE,OAAiB;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEM,IAAI,CAAC,OAAe,EAAE,OAAiB;QAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEM,IAAI,CAAC,OAAe,EAAE,OAAiB;QAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,OAAe,EAAE,OAAiB;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEO,aAAa,CAAC,OAAe,EAAE,KAAa,EAAE,OAAiB;QACrE,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC;IAC1F,CAAC;IAEO,aAAa,CAAC,OAAiB;QACrC,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,OAAO,EAAE,WAAW,EAAE;YACxB,GAAG,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;SACrD;QACD,IAAI,OAAO,EAAE,SAAS,EAAE;YACtB,GAAG,IAAI,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC;SAClC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAnDD,wBAmDC;AAEY,QAAA,GAAG,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACnC,QAAA,YAAY,GAAG,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;AAC9D,QAAA,UAAU,GAAG,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AACzD,QAAA,QAAQ,GAAG,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;AACrD,QAAA,OAAO,GAAG,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;AACnD,QAAA,OAAO,GAAG,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC"}