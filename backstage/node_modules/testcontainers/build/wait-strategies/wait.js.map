{"version": 3, "file": "wait.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/wait.ts"], "names": [], "mappings": ";;;AAAA,uEAAkE;AAClE,6EAAuE;AACvE,uEAAiE;AACjE,6DAAiF;AACjF,2DAA2D;AAC3D,2EAA0E;AAC1E,+DAA0D;AAG1D,MAAa,IAAI;IACR,MAAM,CAAC,MAAM,CAAC,cAA8B;QACjD,OAAO,IAAI,+CAAqB,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAEM,MAAM,CAAC,iBAAiB;QAC7B,OAAO,IAAI,8CAAoB,EAAE,CAAC;IACpC,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,OAAqB,EAAE,KAAK,GAAG,CAAC;QAC1D,OAAO,IAAI,mCAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEM,MAAM,CAAC,cAAc;QAC1B,OAAO,IAAI,oDAAuB,EAAE,CAAC;IACvC,CAAC;IAEM,MAAM,CAAC,iBAAiB;QAC7B,OAAO,IAAI,uDAA2B,EAAE,CAAC;IAC3C,CAAC;IAEM,MAAM,CAAC,OAAO,CACnB,IAAY,EACZ,IAAY,EACZ,UAAmC,EAAE,oBAAoB,EAAE,KAAK,EAAE;QAElE,OAAO,IAAI,qCAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAAC,OAAe;QAChD,OAAO,IAAI,uCAAiB,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;CACF;AAhCD,oBAgCC"}