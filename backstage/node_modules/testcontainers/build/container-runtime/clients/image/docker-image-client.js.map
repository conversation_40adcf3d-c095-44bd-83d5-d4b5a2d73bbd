{"version": 3, "file": "docker-image-client.js", "sourceRoot": "", "sources": ["../../../../src/container-runtime/clients/image/docker-image-client.ts"], "names": [], "mappings": ";;;;;;AAAA,wEAAgD;AAChD,4DAAmC;AACnC,oDAA4B;AAE5B,2BAAgD;AAChD,gDAAwB;AACxB,oDAAyB;AACzB,4CAAyD;AACzD,gEAA2D;AAI3D,MAAa,iBAAiB;IAKP;IACA;IALJ,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;IACnC,eAAe,GAAG,IAAI,oBAAS,EAAE,CAAC;IAEnD,YACqB,SAAoB,EACpB,kBAA0B;QAD1B,cAAS,GAAT,SAAS,CAAW;QACpB,uBAAkB,GAAlB,kBAAkB,CAAQ;IAC5C,CAAC;IAEJ,KAAK,CAAC,KAAK,CAAC,OAAe,EAAE,IAAuB;QAClD,IAAI;YACF,YAAG,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC,mBAAmB,OAAO,MAAM,CAAC,CAAC;YACrE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,SAAS,GAAG,gBAAG,CAAC,IAAI,CAAC,OAAO,EAAE;gBAClC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBAChB,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBACnD,IAAI,YAAY,KAAK,IAAI,CAAC,UAAU,EAAE;wBACpC,OAAO,KAAK,CAAC;qBACd;yBAAM;wBACL,OAAO,eAAe,CAAC,YAAY,CAAC,CAAC;qBACtC;gBACH,CAAC;aACF,CAAC,CAAC;YACH,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBAClC,IAAI,CAAC,SAAS;qBACX,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC;qBAC3B,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC;qBAChC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;oBACf,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC5B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;wBACzB,IAAI,iBAAQ,CAAC,OAAO,EAAE,EAAE;4BACtB,iBAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;yBAC7C;oBACH,CAAC,CAAC,CAAC;oBACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YACH,YAAG,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,CAAC,mBAAmB,OAAO,GAAG,CAAC,CAAC;SAChE;QAAC,OAAO,GAAG,EAAE;YACZ,YAAG,CAAC,KAAK,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;YAC3C,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAEO,KAAK,CAAC,6BAA6B,CAAC,OAAe;QACzD,MAAM,oBAAoB,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QACjE,IAAI,CAAC,IAAA,eAAU,EAAC,oBAAoB,CAAC,EAAE;YACrC,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC;SACpB;QAED,MAAM,oBAAoB,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QAC5F,MAAM,QAAQ,GAAG,IAAA,sBAAY,EAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;QACrD,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAEvC,OAAO,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,SAAoB;QAChC,IAAI;YACF,YAAG,CAAC,KAAK,CAAC,sBAAsB,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5E,YAAG,CAAC,KAAK,CAAC,qBAAqB,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACpD,OAAO,SAAS,CAAC;SAClB;QAAC,OAAO,GAAG,EAAE;YACZ,YAAG,CAAC,KAAK,CAAC,4BAA4B,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3D,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,SAAoB;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;YAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;gBAC7C,OAAO,IAAI,CAAC;aACb;YACD,IAAI;gBACF,YAAG,CAAC,KAAK,CAAC,6BAA6B,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC;gBAC/D,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;gBAC1D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC1C,YAAG,CAAC,KAAK,CAAC,4BAA4B,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,GAAG,YAAY,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC/E,YAAG,CAAC,KAAK,CAAC,4BAA4B,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;oBAC3D,OAAO,KAAK,CAAC;iBACd;gBACD,YAAG,CAAC,KAAK,CAAC,oCAAoC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;gBACnE,MAAM,GAAG,CAAC;aACX;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,SAAoB,EAAE,IAAuD;QACtF,IAAI;YACF,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;gBAClD,YAAG,CAAC,KAAK,CAAC,UAAU,SAAS,CAAC,MAAM,kBAAkB,CAAC,CAAC;gBACxD,OAAO;aACR;YAED,YAAG,CAAC,KAAK,CAAC,kBAAkB,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC;YACpD,MAAM,UAAU,GAAG,MAAM,IAAA,+BAAa,EAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACtF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACzD,UAAU;gBACV,QAAQ,EAAE,IAAI,EAAE,QAAQ;aACzB,CAAC,CAAC;YACH,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBACjC,IAAI,gBAAO,CAAC,OAAO,EAAE,EAAE;wBACrB,gBAAO,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;qBACtD;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,YAAG,CAAC,KAAK,CAAC,iBAAiB,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SACjD;QAAC,OAAO,GAAG,EAAE;YACZ,YAAG,CAAC,KAAK,CAAC,yBAAyB,SAAS,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC;YAChE,MAAM,GAAG,CAAC;SACX;IACH,CAAC;CACF;AAvHD,8CAuHC"}