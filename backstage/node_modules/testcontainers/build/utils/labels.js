"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLabels = exports.LABEL_TESTCONTAINERS_RYUK = exports.LABEL_TESTCONTAINERS_CONTAINER_HASH = exports.LABEL_TESTCONTAINERS_SSHD = exports.LABEL_TESTCONTAINERS_SESSION_ID = exports.LABEL_TESTCONTAINERS_VERSION = exports.LABEL_TESTCONTAINERS_LANG = exports.LABEL_TESTCONTAINERS = void 0;
const version_1 = require("../version");
exports.LABEL_TESTCONTAINERS = "org.testcontainers";
exports.LABEL_TESTCONTAINERS_LANG = "org.testcontainers.lang";
exports.LABEL_TESTCONTAINERS_VERSION = "org.testcontainers.version";
exports.LABEL_TESTCONTAINERS_SESSION_ID = "org.testcontainers.session-id";
exports.LABEL_TESTCONTAINERS_SSHD = "org.testcontainers.sshd";
exports.LABEL_TESTCONTAINERS_CONTAINER_HASH = "org.testcontainers.container-hash";
exports.LABEL_TESTCONTAINERS_RYUK = "org.testcontainers.ryuk";
function createLabels() {
    return {
        [exports.LABEL_TESTCONTAINERS]: "true",
        [exports.LABEL_TESTCONTAINERS_LANG]: "node",
        [exports.LABEL_TESTCONTAINERS_VERSION]: version_1.LIB_VERSION,
    };
}
exports.createLabels = createLabels;
//# sourceMappingURL=labels.js.map