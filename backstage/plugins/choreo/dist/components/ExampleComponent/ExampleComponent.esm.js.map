{"version": 3, "file": "ExampleComponent.esm.js", "sources": ["../../../src/components/ExampleComponent/ExampleComponent.tsx"], "sourcesContent": ["import { Typography, Grid } from '@material-ui/core';\nimport {\n  InfoCard,\n  Header,\n  Page,\n  Content,\n  ContentHeader,\n  HeaderLabel,\n  SupportButton,\n} from '@backstage/core-components';\nimport { ExampleFetchComponent } from '../ExampleFetchComponent';\n\nexport const ExampleComponent = () => (\n  <Page themeId=\"tool\">\n    <Header title=\"Welcome to choreo!\" subtitle=\"Optional subtitle\">\n      <HeaderLabel label=\"Owner\" value=\"Team X\" />\n      <HeaderLabel label=\"Lifecycle\" value=\"Alpha\" />\n    </Header>\n    <Content>\n      <ContentHeader title=\"Plugin title\">\n        <SupportButton>A description of your plugin goes here.</SupportButton>\n      </ContentHeader>\n      <Grid container spacing={3} direction=\"column\">\n        <Grid item>\n          <InfoCard title=\"Information card\">\n            <Typography variant=\"body1\">\n              All content should be wrapped in a card like this.\n            </Typography>\n          </InfoCard>\n        </Grid>\n        <Grid item>\n          <ExampleFetchComponent />\n        </Grid>\n      </Grid>\n    </Content>\n  </Page>\n);\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,gBAAA,GAAmB,sBAC9B,IAAA,CAAC,IAAA,EAAA,EAAK,SAAQ,MAAA,EACZ,QAAA,EAAA;AAAA,kBAAA,IAAA,CAAC,MAAA,EAAA,EAAO,KAAA,EAAM,oBAAA,EAAqB,QAAA,EAAS,mBAAA,EAC1C,QAAA,EAAA;AAAA,oBAAA,GAAA,CAAC,WAAA,EAAA,EAAY,KAAA,EAAM,OAAA,EAAQ,KAAA,EAAM,QAAA,EAAS,CAAA;AAAA,oBAC1C,GAAA,CAAC,WAAA,EAAA,EAAY,KAAA,EAAM,WAAA,EAAY,OAAM,OAAA,EAAQ;AAAA,GAAA,EAC/C,CAAA;AAAA,uBACC,OAAA,EAAA,EACC,QAAA,EAAA;AAAA,oBAAA,GAAA,CAAC,iBAAc,KAAA,EAAM,cAAA,EACnB,QAAA,kBAAA,GAAA,CAAC,aAAA,EAAA,EAAc,qDAAuC,CAAA,EACxD,CAAA;AAAA,yBACC,IAAA,EAAA,EAAK,SAAA,EAAS,MAAC,OAAA,EAAS,CAAA,EAAG,WAAU,QAAA,EACpC,QAAA,EAAA;AAAA,sBAAA,GAAA,CAAC,IAAA,EAAA,EAAK,IAAA,EAAI,IAAA,EACR,QAAA,kBAAA,GAAA,CAAC,QAAA,EAAA,EAAS,KAAA,EAAM,kBAAA,EACd,QAAA,kBAAA,GAAA,CAAC,UAAA,EAAA,EAAW,OAAA,EAAQ,OAAA,EAAQ,QAAA,EAAA,oDAAA,EAE5B,GACF,CAAA,EACF,CAAA;AAAA,0BACC,IAAA,EAAA,EAAK,IAAA,EAAI,IAAA,EACR,QAAA,kBAAA,GAAA,CAAC,yBAAsB,CAAA,EACzB;AAAA,KAAA,EACF;AAAA,GAAA,EACF;AAAA,CAAA,EACF;;;;"}