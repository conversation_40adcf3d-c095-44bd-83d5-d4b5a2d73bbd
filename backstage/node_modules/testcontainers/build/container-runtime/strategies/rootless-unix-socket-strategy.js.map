{"version": 3, "file": "rootless-unix-socket-strategy.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/strategies/rootless-unix-socket-strategy.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAgC;AAChC,4CAAoB;AACpB,gDAAwB;AACxB,yCAAyC;AAIzC,MAAa,0BAA0B;IAElB;IACA;IAFnB,YACmB,WAA4B,OAAO,CAAC,QAAQ,EAC5C,MAAyB,OAAO,CAAC,GAAG;QADpC,aAAQ,GAAR,QAAQ,CAAoC;QAC5C,QAAG,GAAH,GAAG,CAAiC;IACpD,CAAC;IAEJ,OAAO;QACL,OAAO,4BAA4B,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC3D,OAAO;SACR;QAED,MAAM,UAAU,GAAG;YACjB,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,+BAA+B,EAAE;YACtC,IAAI,CAAC,uBAAuB,EAAE;SAC/B;aACE,MAAM,CAAC,kBAAS,CAAC;aACjB,IAAI,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAAC,IAAA,eAAU,EAAC,mBAAmB,CAAC,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,EAAE;YACf,OAAO;SACR;QAED,OAAO;YACL,GAAG,EAAE,UAAU,UAAU,EAAE;YAC3B,aAAa,EAAE,EAAE,UAAU,EAAE;YAC7B,kBAAkB,EAAE,EAAE;YACtB,kBAAkB,EAAE,IAAI;SACzB,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAElD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,OAAO,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;SAChD;aAAM;YACL,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;IAEO,2BAA2B;QACjC,OAAO,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;IAClE,CAAC;IAEO,+BAA+B;QACrC,OAAO,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC;IAEO,uBAAuB;QAC7B,OAAO,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,YAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;CACF;AAzDD,gEAyDC"}