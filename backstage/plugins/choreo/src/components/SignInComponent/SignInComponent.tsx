import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  makeStyles,
  Theme,
} from '@material-ui/core';
import {
  Page,
  Content,
} from '@backstage/core-components';
import {
  AccountCircle as GoogleIcon,
  GitHub as GitHubIcon,
  Business as MicrosoftIcon,
  Domain as EnterpriseIcon,
  Email as EmailIcon,
} from '@material-ui/icons';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    minHeight: '100vh',
    backgroundColor: theme.palette.background.default,
  },
  container: {
    height: '100vh',
    padding: theme.spacing(4),
  },
  signInCard: {
    padding: theme.spacing(3),
    height: 'fit-content',
    maxWidth: 400,
    margin: '0 auto',
  },
  signInTitle: {
    marginBottom: theme.spacing(3),
    textAlign: 'center',
    color: theme.palette.text.primary,
  },
  signInButton: {
    width: '100%',
    marginBottom: theme.spacing(2),
    justifyContent: 'flex-start',
    textTransform: 'none',
    padding: theme.spacing(1.5, 2),
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  buttonIcon: {
    marginRight: theme.spacing(2),
  },
}));

interface SignInOption {
  icon: React.ComponentType;
  label: string;
  id: string;
}

const signInOptions: SignInOption[] = [
  { icon: GoogleIcon, label: 'Continue with Google', id: 'google' },
  { icon: GitHubIcon, label: 'Continue with GitHub', id: 'github' },
  { icon: MicrosoftIcon, label: 'Continue with Microsoft', id: 'microsoft' },
  { icon: EnterpriseIcon, label: 'Sign in with Enterprise ID', id: 'enterprise' },
  { icon: EmailIcon, label: 'Sign in with Email', id: 'email' },
];

export const ChoreoSignInPage: React.FC = () => {
  const classes = useStyles();
  const navigate = useNavigate();

  const handleNavigation = (optionId: string) => {
    // Navigate to home page after sign-in attempt
    navigate('/choreo-home');
  };

  return (
    <Page themeId="home" className={classes.root}>
      <Content className={classes.container}>
        <Grid
          container
          spacing={4}
          style={{ height: '100%' }}
          alignItems="center"
          justifyContent="center"
        >
          <Grid item xs={12} md={6} lg={4}>
            <Card className={classes.signInCard} elevation={2}>
              <CardContent>
                <Typography variant="h4" className={classes.signInTitle}>
                  Sign In to Choreo
                </Typography>
                <Typography 
                  variant="body2" 
                  color="textSecondary" 
                  align="center"
                  style={{ marginBottom: 24 }}
                >
                  Choose your preferred sign-in method
                </Typography>
                {signInOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <Button
                      key={option.id}
                      variant="outlined"
                      className={classes.signInButton}
                      startIcon={<IconComponent className={classes.buttonIcon} />}
                      onClick={() => handleNavigation(option.id)}
                      aria-label={`Sign in with ${option.id}`}
                      data-testid={`signin-${option.id}`}
                    >
                      {option.label}
                    </Button>
                  );
                })}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};
