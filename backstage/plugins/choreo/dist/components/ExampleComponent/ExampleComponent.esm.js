import { jsxs, jsx } from 'react/jsx-runtime';
import { Grid, Typography } from '@material-ui/core';
import { <PERSON>, Header, HeaderLabel, Content, ContentHeader, SupportButton, InfoCard } from '@backstage/core-components';
import { ExampleFetchComponent } from '../ExampleFetchComponent/ExampleFetchComponent.esm.js';

const ExampleComponent = () => /* @__PURE__ */ jsxs(Page, { themeId: "tool", children: [
  /* @__PURE__ */ jsxs(Header, { title: "Welcome to choreo!", subtitle: "Optional subtitle", children: [
    /* @__PURE__ */ jsx(HeaderLabel, { label: "Owner", value: "Team X" }),
    /* @__PURE__ */ jsx(HeaderLabel, { label: "Lifecycle", value: "Alpha" })
  ] }),
  /* @__PURE__ */ jsxs(Content, { children: [
    /* @__PURE__ */ jsx(ContentHeader, { title: "Plugin title", children: /* @__PURE__ */ jsx(SupportButton, { children: "A description of your plugin goes here." }) }),
    /* @__PURE__ */ jsxs(Grid, { container: true, spacing: 3, direction: "column", children: [
      /* @__PURE__ */ jsx(Grid, { item: true, children: /* @__PURE__ */ jsx(InfoCard, { title: "Information card", children: /* @__PURE__ */ jsx(Typography, { variant: "body1", children: "All content should be wrapped in a card like this." }) }) }),
      /* @__PURE__ */ jsx(Grid, { item: true, children: /* @__PURE__ */ jsx(ExampleFetchComponent, {}) })
    ] })
  ] })
] });

export { ExampleComponent };
//# sourceMappingURL=ExampleComponent.esm.js.map
