# Choreo Plugin - Routing Integration Guide

This document explains how to integrate the new SignInComponent and HomeComponent into your Backstage application routing.

## Components Created

### 1. SignInComponent
- **Path**: `src/components/SignInComponent/`
- **Target Route**: `/choreo-signin`
- **Features**:
  - Five sign-in options (Google, GitHub, Microsoft, Enterprise ID, Email)
  - Responsive design with Material-UI theming
  - Proper accessibility attributes
  - Navigation to `/choreo-home` on button click
  - TypeScript interfaces and comprehensive testing

### 2. HomeComponent
- **Path**: `src/components/HomeComponent/`
- **Target Route**: `/choreo-home`
- **Features**:
  - Placeholder dashboard layout
  - Customizable title and subtitle props
  - Ready for future feature additions
  - Proper TypeScript typing and testing

## Integration Steps

### Option 1: Add Routes to Main App.tsx (Recommended)

Add the following imports to your `packages/app/src/App.tsx`:

```typescript
import { SignInComponent, HomeComponent } from '@internal/plugin-choreo';
```

Add the following routes to your `FlatRoutes` component:

```typescript
const routes = (
  <FlatRoutes>
    {/* ... existing routes ... */}
    <Route path="/choreo-signin" element={<SignInComponent />} />
    <Route path="/choreo-home" element={<HomeComponent />} />
    {/* ... existing routes ... */}
  </FlatRoutes>
);
```

### Option 2: Create Route References (Advanced)

If you prefer a more plugin-centric approach, you can create additional route references:

1. Update `src/routes.ts`:
```typescript
import { createRouteRef } from '@backstage/core-plugin-api';

export const rootRouteRef = createRouteRef({
  id: 'choreo',
});

export const signInRouteRef = createRouteRef({
  id: 'choreo-signin',
});

export const homeRouteRef = createRouteRef({
  id: 'choreo-home',
});
```

2. Update `src/plugin.ts` to create routable extensions:
```typescript
export const ChoreoSignInPage = choreoPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoSignInPage',
    component: () =>
      import('./components/SignInComponent').then(m => m.SignInComponent),
    mountPoint: signInRouteRef,
  }),
);

export const ChoreoHomePage = choreoPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoHomePage',
    component: () =>
      import('./components/HomeComponent').then(m => m.HomeComponent),
    mountPoint: homeRouteRef,
  }),
);
```

## Navigation Flow

1. User visits `/choreo-signin`
2. User clicks any sign-in option
3. Application navigates to `/choreo-home`
4. User sees the dashboard placeholder

## Testing

Both components include comprehensive tests:

```bash
# Run tests for the new components
npm test -- --testPathPattern="SignInComponent|HomeComponent"

# Run all tests
npm test
```

## Customization

### SignInComponent
- Icons can be replaced with custom ones
- Sign-in options can be modified in the `signInOptions` array
- Styling can be customized through the `useStyles` hook

### HomeComponent
- Accepts `title` and `subtitle` props for customization
- Layout can be extended with additional InfoCard components
- Ready for integration with real dashboard data

## Next Steps

1. Add the routes to your App.tsx (Option 1 recommended)
2. Test navigation between components
3. Customize styling and content as needed
4. Integrate with actual authentication providers
5. Add real dashboard functionality to HomeComponent

## Dependencies

The components use the following dependencies (already included in package.json):
- `@backstage/core-components`
- `@material-ui/core`
- `@material-ui/icons`
- `react-router-dom` (peer dependency)
