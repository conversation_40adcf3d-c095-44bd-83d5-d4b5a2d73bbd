<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755000870403" clover="3.2.0">
  <project timestamp="1755000870403" name="All files">
    <metrics statements="174" coveredstatements="157" conditionals="14" coveredconditionals="5" methods="27" coveredmethods="16" elements="215" coveredelements="178" complexity="0" loc="174" ncloc="174" packages="9" files="18" classes="18"/>
    <package name="packages.app.src">
      <metrics statements="33" coveredstatements="31" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <file name="App.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/App.tsx">
        <metrics statements="28" coveredstatements="28" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
      </file>
      <file name="apis.ts" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/apis.ts">
        <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
      </file>
      <file name="index.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/index.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
      </file>
    </package>
    <package name="packages.app.src.components.Root">
      <metrics statements="31" coveredstatements="27" conditionals="2" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="LogoFull.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoFull.tsx">
        <metrics statements="5" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
      </file>
      <file name="LogoIcon.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoIcon.tsx">
        <metrics statements="5" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
      </file>
      <file name="Root.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/Root.tsx">
        <metrics statements="20" coveredstatements="18" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/index.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
      </file>
    </package>
    <package name="packages.app.src.components.catalog">
      <metrics statements="12" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="EntityPage.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/catalog/EntityPage.tsx">
        <metrics statements="12" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
      </file>
    </package>
    <package name="packages.app.src.components.search">
      <metrics statements="18" coveredstatements="11" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="SearchPage.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/search/SearchPage.tsx">
        <metrics statements="18" coveredstatements="11" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
      </file>
    </package>
    <package name="plugins.choreo-backend.src">
      <metrics statements="27" coveredstatements="26" conditionals="1" coveredconditionals="0" methods="6" coveredmethods="6"/>
      <file name="plugin.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/plugin.ts">
        <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="17" count="8" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
      </file>
      <file name="router.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/router.ts">
        <metrics statements="19" coveredstatements="18" conditionals="1" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="8" count="4" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="4" type="stmt"/>
        <line num="24" count="4" type="stmt"/>
        <line num="29" count="4" type="stmt"/>
        <line num="30" count="4" type="stmt"/>
        <line num="31" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="4" type="stmt"/>
        <line num="39" count="3" type="stmt"/>
        <line num="42" count="4" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="50" count="4" type="stmt"/>
      </file>
    </package>
    <package name="plugins.choreo-backend.src.services.TodoListService">
      <metrics statements="25" coveredstatements="23" conditionals="5" coveredconditionals="2" methods="5" coveredmethods="5"/>
      <file name="createTodoListService.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/createTodoListService.ts">
        <metrics statements="24" coveredstatements="22" conditionals="5" coveredconditionals="2" methods="5" coveredmethods="5"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="12" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="31" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="0" type="stmt"/>
        <line num="56" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="57" count="1" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="61" count="2" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="69" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/index.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
      </file>
    </package>
    <package name="plugins.choreo.src">
      <metrics statements="7" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="plugin.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/plugin.ts">
        <metrics statements="5" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
      </file>
      <file name="routes.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/routes.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
      </file>
    </package>
    <package name="plugins.choreo.src.components.ExampleComponent">
      <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="ExampleComponent.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleComponent/ExampleComponent.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
      </file>
    </package>
    <package name="plugins.choreo.src.components.ExampleFetchComponent">
      <metrics statements="17" coveredstatements="17" conditionals="5" coveredconditionals="3" methods="4" coveredmethods="4"/>
      <file name="ExampleFetchComponent.tsx" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/ExampleFetchComponent.tsx">
        <metrics statements="16" coveredstatements="16" conditionals="5" coveredconditionals="3" methods="4" coveredmethods="4"/>
        <line num="1" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="235" count="2" type="stmt"/>
        <line num="259" count="2" type="stmt"/>
        <line num="260" count="2" type="stmt"/>
        <line num="262" count="2" type="stmt"/>
        <line num="269" count="2" type="stmt"/>
        <line num="270" count="40" type="stmt"/>
        <line num="294" count="3" type="stmt"/>
        <line num="296" count="6" type="stmt"/>
        <line num="298" count="2" type="stmt"/>
        <line num="301" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="302" count="2" type="stmt"/>
        <line num="303" count="2" type="cond" truecount="0" falsecount="1"/>
      </file>
      <file name="index.ts" path="/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/index.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
