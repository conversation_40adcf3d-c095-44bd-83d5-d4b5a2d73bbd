import { screen } from '@testing-library/react';
import { ChoreoHomePage } from './HomeComponent';
import {
  renderInTestApp,
  TestApiProvider,
} from '@backstage/test-utils';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ChoreoHomePage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('should render with default props', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage />
      </TestApiProvider>
    );

    expect(screen.getByText('Welcome to Choreo')).toBeInTheDocument();
    expect(screen.getByText('Your development platform dashboard')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('should render with custom title and subtitle', async () => {
    const customTitle = 'Custom Choreo Title';
    const customSubtitle = 'Custom subtitle text';

    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage title={customTitle} subtitle={customSubtitle} />
      </TestApiProvider>
    );

    expect(screen.getByText(customTitle)).toBeInTheDocument();
    expect(screen.getByText(customSubtitle)).toBeInTheDocument();
  });

  it('should render welcome message and placeholder content', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage />
      </TestApiProvider>
    );

    expect(screen.getByText('Getting Started')).toBeInTheDocument();
    expect(screen.getByText('Welcome to Choreo! You have successfully signed in to your account.')).toBeInTheDocument();
    expect(screen.getByText('This is a placeholder home page. Future features will include:')).toBeInTheDocument();
  });

  it('should render feature list items', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage />
      </TestApiProvider>
    );

    expect(screen.getByText('Project management dashboard')).toBeInTheDocument();
    expect(screen.getByText('Service deployment status')).toBeInTheDocument();
    expect(screen.getByText('Analytics and monitoring')).toBeInTheDocument();
    expect(screen.getByText('Team collaboration tools')).toBeInTheDocument();
  });

  it('should render quick actions section', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage />
      </TestApiProvider>
    );

    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Quick action buttons and shortcuts will be available here.')).toBeInTheDocument();
  });

  it('should render content header with description', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <ChoreoHomePage />
      </TestApiProvider>
    );

    expect(screen.getByText('Welcome to your Choreo dashboard. This is where you\'ll manage your projects and services.')).toBeInTheDocument();
  });
});