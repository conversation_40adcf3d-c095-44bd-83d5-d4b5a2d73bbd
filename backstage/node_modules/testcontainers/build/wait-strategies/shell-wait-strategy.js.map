{"version": 3, "file": "shell-wait-strategy.js", "sourceRoot": "", "sources": ["../../src/wait-strategies/shell-wait-strategy.ts"], "names": [], "mappings": ";;;AACA,sCAA+C;AAC/C,4DAAiE;AACjE,mDAAuD;AAEvD,MAAa,iBAAkB,SAAQ,oCAAoB;IAC5B;IAA7B,YAA6B,OAAe;QAC1C,KAAK,EAAE,CAAC;QADmB,YAAO,GAAP,OAAO,CAAQ;IAE5C,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,SAA8B;QACxD,YAAG,CAAC,KAAK,CAAC,yCAAyC,IAAI,CAAC,OAAO,MAAM,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACtG,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QAEjD,MAAM,IAAI,sBAAa,CAAgB,GAAG,CAAC,CAAC,UAAU,CACpD,KAAK,IAAI,EAAE;YACT,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC3F,GAAG,EAAE,KAAK;aACX,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,KAAK,CAAC,EAC5B,GAAG,EAAE;YACH,MAAM,OAAO,GAAG,kBAAkB,IAAI,CAAC,OAAO,0BAA0B,IAAI,CAAC,cAAc,IAAI,CAAC;YAChG,YAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC,EACD,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,YAAG,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3E,CAAC;CACF;AA3BD,8CA2BC"}