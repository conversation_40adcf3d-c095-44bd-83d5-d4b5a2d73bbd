{"version": 3, "file": "started-generic-container.js", "sourceRoot": "", "sources": ["../../src/generic-container/started-generic-container.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAgC;AAChC,4DAAmC;AAGnC,sCAA8C;AAC9C,4DAAyF;AACzF,6CAA6C;AAG7C,sDAAkD;AAClD,4CAAkE;AAClE,oEAA+D;AAC/D,8EAAyE;AAEzE,2EAAsE;AAEtE,MAAa,uBAAuB;IAKf;IACA;IACT;IACA;IACS;IACA;IACA;IAVX,gBAAgB,CAAwB;IAC/B,iBAAiB,GAAG,IAAI,oBAAS,EAAE,CAAC;IAErD,YACmB,SAA8B,EAC9B,IAAY,EACrB,aAAmC,EACnC,UAAsB,EACb,IAAY,EACZ,YAA0B,EAC1B,UAAmB;QANnB,cAAS,GAAT,SAAS,CAAqB;QAC9B,SAAI,GAAJ,IAAI,CAAQ;QACrB,kBAAa,GAAb,aAAa,CAAsB;QACnC,eAAU,GAAV,UAAU,CAAY;QACb,SAAI,GAAJ,IAAI,CAAQ;QACZ,iBAAY,GAAZ,YAAY,CAAc;QAC1B,eAAU,GAAV,UAAU,CAAS;IACnC,CAAC;IAIG,KAAK,CAAC,IAAI,CAAC,UAAgC,EAAE;QAClD,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;YACvD,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC;aAC9B;YACD,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gCAAgC,CAAC,OAI9C;QACC,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACtC,IAAI,YAAY,EAAE;YAChB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,wCAA+B,CAAC,CAAC;YAClE,IAAI,CAAC,SAAS,EAAE;gBACd,SAAS,GAAG,MAAM,IAAA,kBAAS,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aACxE;YACD,OAAO,CAAC,IAAI,CAAC,SAAS,wCAA+B,IAAI,SAAS,EAAE,CAAC,CAAC;SACvE;aAAM,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,wCAA+B,CAAC,EAAE;YAC7E,oGAAoG;YACpG,iEAAiE;YACjE,OAAO,CAAC,IAAI,CAAC,SAAS,wCAA+B,GAAG,CAAC,CAAC;SAC3D;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,OAAsB;QACxC,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,EAAE,YAAY,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC;QAChE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QACtG,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;QAC1G,OAAO,OAAO,CAAC;IACjB,CAAC;IAIM,KAAK,CAAC,OAAO,CAAC,UAAmC,EAAE;QACxD,YAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACxE,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,eAAe,GAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC;QACnE,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAEhE,IAAI,CAAC,aAAa,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,MAAM,mBAAmB,GAAG,IAAA,qCAAgB,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAE/D,IAAI,qBAAY,CAAC,OAAO,EAAE,EAAE;YAC1B,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;iBACjF,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,qBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;iBACzF,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,qBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAC7F;QAED,IAAI,CAAC,UAAU,GAAG,wBAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,MAAM,CAC9G,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC;QAEF,MAAM,IAAA,qCAAgB,EAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC9F,YAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IACtE,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,UAAgC,EAAE;QAC5D,YAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAClC;QAED,MAAM,eAAe,GAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC;QAC9G,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAClF,IAAI,eAAe,CAAC,MAAM,EAAE;YAC1B,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC;SACjG;QACD,YAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;SACjC;QAED,OAAO,IAAI,mDAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;IAC3C,CAAC;IAEM,aAAa,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;IAC3B,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;IAC1C,CAAC;IAEM,eAAe;QACpB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAChD,CAAC;IAEM,YAAY,CAAC,WAAmB;QACrC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC;IAC1D,CAAC;IAEM,YAAY,CAAC,WAAmB;QACrC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC;IAC1D,CAAC;IAEO,kBAAkB;QACxB,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC;aAC/D,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAChC,CAAC,WAAW,CAAC,EAAE;gBACb,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B;SACF,CAAC,CAAC;aACF,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,WAAyB;QACzD,YAAG,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/E,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,GAAG,GAAG,IAAA,kBAAQ,EAAC,KAAK,CAAC,CAAC;QAC5B,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAChF,GAAG,CAAC,QAAQ,EAAE,CAAC;QACf,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5D,YAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7E,CAAC;IAEM,KAAK,CAAC,0BAA0B,CAAC,iBAAoC;QAC1E,YAAG,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACrF,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,GAAG,GAAG,IAAA,kBAAQ,EAAC,KAAK,CAAC,CAAC;QAC5B,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QACjF,GAAG,CAAC,QAAQ,EAAE,CAAC;QACf,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5D,YAAG,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IACnF,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,cAA+B;QACjE,YAAG,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACjF,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,GAAG,GAAG,IAAA,kBAAQ,EAAC,KAAK,CAAC,CAAC;QAC5B,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACzG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACf,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5D,YAAG,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/E,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,GAAa,EAAE,MAAM,GAAG,GAAG;QAC7D,YAAG,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACjF,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAC/D,YAAG,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/E,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,IAAY;QAChD,YAAG,CAAC,KAAK,CAAC,oBAAoB,IAAI,qBAAqB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7F,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACzE,YAAG,CAAC,KAAK,CAAC,mBAAmB,IAAI,kBAAkB,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACzF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,OAA0B,EAAE,IAA2B;QACvE,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzE,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QACjD,YAAG,CAAC,KAAK,CAAC,sBAAsB,UAAU,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QACtF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC7E,YAAG,CAAC,KAAK,CAAC,qBAAqB,UAAU,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAErF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAwC;QACxD,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAyB,GAAE,CAAC;QAEjD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;CACF;AA1ND,0DA0NC"}