{"version": 3, "file": "ExampleFetchComponent.esm.js", "sources": ["../../../src/components/ExampleFetchComponent/ExampleFetchComponent.tsx"], "sourcesContent": ["import { makeStyles } from '@material-ui/core/styles';\nimport {\n  Table,\n  TableColumn,\n  Progress,\n  ResponseErrorPanel,\n} from '@backstage/core-components';\nimport useAsync from 'react-use/lib/useAsync';\n\nexport const exampleUsers = {\n  results: [\n    {\n      gender: 'female',\n      name: {\n        title: 'Miss',\n        first: '<PERSON>',\n        last: '<PERSON>',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Carolyn',\n      nat: 'GB',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: '<PERSON><PERSON>',\n        last: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Esma',\n      nat: 'TR',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: '<PERSON>',\n        last: '<PERSON>',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=<PERSON>',\n      nat: 'GB',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: '<PERSON>',\n        last: '<PERSON>',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Derrick',\n      nat: 'IE',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Miss',\n        first: '<PERSON>ie',\n        last: 'Lambert',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Mattie',\n      nat: 'AU',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Mijat',\n        last: 'Rakić',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Mijat',\n      nat: 'RS',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Javier',\n        last: 'Reid',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Javier',\n      nat: 'US',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: 'Isabella',\n        last: 'Li',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Isabella',\n      nat: 'CA',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Mrs',\n        first: 'Stephanie',\n        last: 'Garrett',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Stephanie',\n      nat: 'AU',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: 'Antonia',\n        last: 'Núñez',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Antonia',\n      nat: 'ES',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Donald',\n        last: 'Young',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Donald',\n      nat: 'US',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Iegor',\n        last: 'Holodovskiy',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Iegor',\n      nat: 'UA',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Madame',\n        first: 'Jessica',\n        last: 'David',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Jessica',\n      nat: 'CH',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: 'Eve',\n        last: 'Martinez',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Eve',\n      nat: 'FR',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Caleb',\n        last: 'Silva',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Caleb',\n      nat: 'US',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Miss',\n        first: 'Marcia',\n        last: 'Jenkins',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Marcia',\n      nat: 'US',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Mrs',\n        first: 'Mackenzie',\n        last: 'Jones',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Mackenzie',\n      nat: 'NZ',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Jeremiah',\n        last: 'Gutierrez',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Jeremiah',\n      nat: 'AU',\n    },\n    {\n      gender: 'female',\n      name: {\n        title: 'Ms',\n        first: 'Luciara',\n        last: 'Souza',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Luciara',\n      nat: 'BR',\n    },\n    {\n      gender: 'male',\n      name: {\n        title: 'Mr',\n        first: 'Valgi',\n        last: 'da Cunha',\n      },\n      email: '<EMAIL>',\n      picture: 'https://api.dicebear.com/6.x/open-peeps/svg?seed=Valgi',\n      nat: 'BR',\n    },\n  ],\n};\n\nconst useStyles = makeStyles({\n  avatar: {\n    height: 32,\n    width: 32,\n    borderRadius: '50%',\n  },\n});\n\ntype User = {\n  gender: string; // \"male\"\n  name: {\n    title: string; // \"Mr\",\n    first: string; // \"Duane\",\n    last: string; // \"Reed\"\n  };\n  email: string; // \"<EMAIL>\"\n  picture: string; // \"https://api.dicebear.com/6.x/open-peeps/svg?seed=Duane\"\n  nat: string; // \"AU\"\n};\n\ntype DenseTableProps = {\n  users: User[];\n};\n\nexport const DenseTable = ({ users }: DenseTableProps) => {\n  const classes = useStyles();\n\n  const columns: TableColumn[] = [\n    { title: 'Avatar', field: 'avatar' },\n    { title: 'Name', field: 'name' },\n    { title: 'Email', field: 'email' },\n    { title: 'Nationality', field: 'nationality' },\n  ];\n\n  const data = users.map(user => {\n    return {\n      avatar: (\n        <img\n          src={user.picture}\n          className={classes.avatar}\n          alt={user.name.first}\n        />\n      ),\n      name: `${user.name.first} ${user.name.last}`,\n      email: user.email,\n      nationality: user.nat,\n    };\n  });\n\n  return (\n    <Table\n      title=\"Example User List\"\n      options={{ search: false, paging: false }}\n      columns={columns}\n      data={data}\n    />\n  );\n};\n\nexport const ExampleFetchComponent = () => {\n\n  const { value, loading, error } = useAsync(async (): Promise<User[]> => {\n    // Would use fetch in a real world example\n    return exampleUsers.results;\n  }, []);\n\n  if (loading) {\n    return <Progress />;\n  } else if (error) {\n    return <ResponseErrorPanel error={error} />;\n  }\n\n  return <DenseTable users={value || []} />;\n};\n"], "names": [], "mappings": ";;;;;AASO,MAAM,YAAA,GAAe;AAAA,EAC1B,OAAA,EAAS;AAAA,IACP;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,MAAA;AAAA,QACP,KAAA,EAAO,SAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,2BAAA;AAAA,MACP,OAAA,EAAS,0DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,MAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,6BAAA;AAAA,MACP,OAAA,EAAS,uDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,UAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,6BAAA;AAAA,MACP,OAAA,EAAS,2DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,SAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,4BAAA;AAAA,MACP,OAAA,EAAS,0DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,MAAA;AAAA,QACP,KAAA,EAAO,QAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,4BAAA;AAAA,MACP,OAAA,EAAS,yDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,OAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,yBAAA;AAAA,MACP,OAAA,EAAS,wDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,QAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,yBAAA;AAAA,MACP,OAAA,EAAS,yDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,UAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,yBAAA;AAAA,MACP,OAAA,EAAS,2DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,KAAA;AAAA,QACP,KAAA,EAAO,WAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,+BAAA;AAAA,MACP,OAAA,EAAS,4DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,SAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,2BAAA;AAAA,MACP,OAAA,EAAS,0DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,QAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,0BAAA;AAAA,MACP,OAAA,EAAS,yDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,OAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,+BAAA;AAAA,MACP,OAAA,EAAS,wDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,QAAA;AAAA,QACP,KAAA,EAAO,SAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,2BAAA;AAAA,MACP,OAAA,EAAS,0DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,KAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,0BAAA;AAAA,MACP,OAAA,EAAS,sDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,OAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,yBAAA;AAAA,MACP,OAAA,EAAS,wDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,MAAA;AAAA,QACP,KAAA,EAAO,QAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,4BAAA;AAAA,MACP,OAAA,EAAS,yDAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,KAAA;AAAA,QACP,KAAA,EAAO,WAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,6BAAA;AAAA,MACP,OAAA,EAAS,4DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,UAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,gCAAA;AAAA,MACP,OAAA,EAAS,2DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,QAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,SAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,2BAAA;AAAA,MACP,OAAA,EAAS,0DAAA;AAAA,MACT,GAAA,EAAK;AAAA,KACP;AAAA,IACA;AAAA,MACE,MAAA,EAAQ,MAAA;AAAA,MACR,IAAA,EAAM;AAAA,QACJ,KAAA,EAAO,IAAA;AAAA,QACP,KAAA,EAAO,OAAA;AAAA,QACP,IAAA,EAAM;AAAA,OACR;AAAA,MACA,KAAA,EAAO,2BAAA;AAAA,MACP,OAAA,EAAS,wDAAA;AAAA,MACT,GAAA,EAAK;AAAA;AACP;AAEJ;AAEA,MAAM,YAAY,UAAA,CAAW;AAAA,EAC3B,MAAA,EAAQ;AAAA,IACN,MAAA,EAAQ,EAAA;AAAA,IACR,KAAA,EAAO,EAAA;AAAA,IACP,YAAA,EAAc;AAAA;AAElB,CAAC,CAAA;AAkBM,MAAM,UAAA,GAAa,CAAC,EAAE,KAAA,EAAM,KAAuB;AACxD,EAAA,MAAM,UAAU,SAAA,EAAU;AAE1B,EAAA,MAAM,OAAA,GAAyB;AAAA,IAC7B,EAAE,KAAA,EAAO,QAAA,EAAU,KAAA,EAAO,QAAA,EAAS;AAAA,IACnC,EAAE,KAAA,EAAO,MAAA,EAAQ,KAAA,EAAO,MAAA,EAAO;AAAA,IAC/B,EAAE,KAAA,EAAO,OAAA,EAAS,KAAA,EAAO,OAAA,EAAQ;AAAA,IACjC,EAAE,KAAA,EAAO,aAAA,EAAe,KAAA,EAAO,aAAA;AAAc,GAC/C;AAEA,EAAA,MAAM,IAAA,GAAO,KAAA,CAAM,GAAA,CAAI,CAAA,IAAA,KAAQ;AAC7B,IAAA,OAAO;AAAA,MACL,MAAA,kBACE,GAAA;AAAA,QAAC,KAAA;AAAA,QAAA;AAAA,UACC,KAAK,IAAA,CAAK,OAAA;AAAA,UACV,WAAW,OAAA,CAAQ,MAAA;AAAA,UACnB,GAAA,EAAK,KAAK,IAAA,CAAK;AAAA;AAAA,OACjB;AAAA,MAEF,IAAA,EAAM,GAAG,IAAA,CAAK,IAAA,CAAK,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,KAAK,IAAI,CAAA,CAAA;AAAA,MAC1C,OAAO,IAAA,CAAK,KAAA;AAAA,MACZ,aAAa,IAAA,CAAK;AAAA,KACpB;AAAA,EACF,CAAC,CAAA;AAED,EAAA,uBACE,GAAA;AAAA,IAAC,KAAA;AAAA,IAAA;AAAA,MACC,KAAA,EAAM,mBAAA;AAAA,MACN,OAAA,EAAS,EAAE,MAAA,EAAQ,KAAA,EAAO,QAAQ,KAAA,EAAM;AAAA,MACxC,OAAA;AAAA,MACA;AAAA;AAAA,GACF;AAEJ;AAEO,MAAM,wBAAwB,MAAM;AAEzC,EAAA,MAAM,EAAE,KAAA,EAAO,OAAA,EAAS,KAAA,EAAM,GAAI,SAAS,YAA6B;AAEtE,IAAA,OAAO,YAAA,CAAa,OAAA;AAAA,EACtB,CAAA,EAAG,EAAE,CAAA;AAEL,EAAA,IAAI,OAAA,EAAS;AACX,IAAA,2BAAQ,QAAA,EAAA,EAAS,CAAA;AAAA,EACnB,WAAW,KAAA,EAAO;AAChB,IAAA,uBAAO,GAAA,CAAC,sBAAmB,KAAA,EAAc,CAAA;AAAA,EAC3C;AAEA,EAAA,uBAAO,GAAA,CAAC,UAAA,EAAA,EAAW,KAAA,EAAO,KAAA,IAAS,EAAC,EAAG,CAAA;AACzC;;;;"}