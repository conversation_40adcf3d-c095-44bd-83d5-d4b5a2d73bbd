{"version": 3, "file": "SignInComponent.esm.js", "sources": ["../../../src/components/SignInComponent/SignInComponent.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Typo<PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  makeStyles,\n  Theme,\n} from '@material-ui/core';\nimport {\n  Page,\n  Content,\n} from '@backstage/core-components';\nimport {\n  AccountCircle as GoogleIcon,\n  GitHub as GitHubIcon,\n  Business as MicrosoftIcon,\n  Domain as EnterpriseIcon,\n  Email as EmailIcon,\n} from '@material-ui/icons';\n\nconst useStyles = makeStyles((theme: Theme) => ({\n  root: {\n    minHeight: '100vh',\n    backgroundColor: theme.palette.background.default,\n  },\n  container: {\n    height: '100vh',\n    padding: theme.spacing(4),\n  },\n  signInCard: {\n    padding: theme.spacing(3),\n    height: 'fit-content',\n    maxWidth: 400,\n    margin: '0 auto',\n  },\n  signInTitle: {\n    marginBottom: theme.spacing(3),\n    textAlign: 'center',\n    color: theme.palette.text.primary,\n  },\n  signInButton: {\n    width: '100%',\n    marginBottom: theme.spacing(2),\n    justifyContent: 'flex-start',\n    textTransform: 'none',\n    padding: theme.spacing(1.5, 2),\n    '&:hover': {\n      backgroundColor: theme.palette.action.hover,\n    },\n  },\n  buttonIcon: {\n    marginRight: theme.spacing(2),\n  },\n}));\n\ninterface SignInOption {\n  icon: React.ComponentType;\n  label: string;\n  id: string;\n}\n\nconst signInOptions: SignInOption[] = [\n  { icon: GoogleIcon, label: 'Continue with Google', id: 'google' },\n  { icon: GitHubIcon, label: 'Continue with GitHub', id: 'github' },\n  { icon: MicrosoftIcon, label: 'Continue with Microsoft', id: 'microsoft' },\n  { icon: EnterpriseIcon, label: 'Sign in with Enterprise ID', id: 'enterprise' },\n  { icon: EmailIcon, label: 'Sign in with Email', id: 'email' },\n];\n\nexport const SignInComponent: React.FC = () => {\n  const classes = useStyles();\n  const navigate = useNavigate();\n\n  const handleNavigation = (optionId: string) => {\n    // Navigate to home page after sign-in attempt\n    navigate('/choreo-home');\n  };\n\n  return (\n    <Page themeId=\"home\" className={classes.root}>\n      <Content className={classes.container}>\n        <Grid\n          container\n          spacing={4}\n          style={{ height: '100%' }}\n          alignItems=\"center\"\n          justifyContent=\"center\"\n        >\n          <Grid item xs={12} md={6} lg={4}>\n            <Card className={classes.signInCard} elevation={2}>\n              <CardContent>\n                <Typography variant=\"h4\" className={classes.signInTitle}>\n                  Sign In to Choreo\n                </Typography>\n                <Typography \n                  variant=\"body2\" \n                  color=\"textSecondary\" \n                  align=\"center\"\n                  style={{ marginBottom: 24 }}\n                >\n                  Choose your preferred sign-in method\n                </Typography>\n                {signInOptions.map((option) => {\n                  const IconComponent = option.icon;\n                  return (\n                    <Button\n                      key={option.id}\n                      variant=\"outlined\"\n                      className={classes.signInButton}\n                      startIcon={<IconComponent className={classes.buttonIcon} />}\n                      onClick={() => handleNavigation(option.id)}\n                      aria-label={`Sign in with ${option.id}`}\n                      data-testid={`signin-${option.id}`}\n                    >\n                      {option.label}\n                    </Button>\n                  );\n                })}\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Content>\n    </Page>\n  );\n};\n"], "names": ["GoogleIcon", "GitHubIcon", "MicrosoftIcon", "EnterpriseIcon", "EmailIcon"], "mappings": ";;;;;;AAuBA,MAAM,SAAA,GAAY,UAAA,CAAW,CAAC,KAAA,MAAkB;AAAA,EAC9C,IAAA,EAAM;AAAA,IACJ,SAAA,EAAW,OAAA;AAAA,IACX,eAAA,EAAiB,KAAA,CAAM,OAAA,CAAQ,UAAA,CAAW;AAAA,GAC5C;AAAA,EACA,SAAA,EAAW;AAAA,IACT,MAAA,EAAQ,OAAA;AAAA,IACR,OAAA,EAAS,KAAA,CAAM,OAAA,CAAQ,CAAC;AAAA,GAC1B;AAAA,EACA,UAAA,EAAY;AAAA,IACV,OAAA,EAAS,KAAA,CAAM,OAAA,CAAQ,CAAC,CAAA;AAAA,IACxB,MAAA,EAAQ,aAAA;AAAA,IACR,QAAA,EAAU,GAAA;AAAA,IACV,MAAA,EAAQ;AAAA,GACV;AAAA,EACA,WAAA,EAAa;AAAA,IACX,YAAA,EAAc,KAAA,CAAM,OAAA,CAAQ,CAAC,CAAA;AAAA,IAC7B,SAAA,EAAW,QAAA;AAAA,IACX,KAAA,EAAO,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK;AAAA,GAC5B;AAAA,EACA,YAAA,EAAc;AAAA,IACZ,KAAA,EAAO,MAAA;AAAA,IACP,YAAA,EAAc,KAAA,CAAM,OAAA,CAAQ,CAAC,CAAA;AAAA,IAC7B,cAAA,EAAgB,YAAA;AAAA,IAChB,aAAA,EAAe,MAAA;AAAA,IACf,OAAA,EAAS,KAAA,CAAM,OAAA,CAAQ,GAAA,EAAK,CAAC,CAAA;AAAA,IAC7B,SAAA,EAAW;AAAA,MACT,eAAA,EAAiB,KAAA,CAAM,OAAA,CAAQ,MAAA,CAAO;AAAA;AACxC,GACF;AAAA,EACA,UAAA,EAAY;AAAA,IACV,WAAA,EAAa,KAAA,CAAM,OAAA,CAAQ,CAAC;AAAA;AAEhC,CAAA,CAAE,CAAA;AAQF,MAAM,aAAA,GAAgC;AAAA,EACpC,EAAE,IAAA,EAAMA,aAAA,EAAY,KAAA,EAAO,sBAAA,EAAwB,IAAI,QAAA,EAAS;AAAA,EAChE,EAAE,IAAA,EAAMC,MAAA,EAAY,KAAA,EAAO,sBAAA,EAAwB,IAAI,QAAA,EAAS;AAAA,EAChE,EAAE,IAAA,EAAMC,QAAA,EAAe,KAAA,EAAO,yBAAA,EAA2B,IAAI,WAAA,EAAY;AAAA,EACzE,EAAE,IAAA,EAAMC,MAAA,EAAgB,KAAA,EAAO,4BAAA,EAA8B,IAAI,YAAA,EAAa;AAAA,EAC9E,EAAE,IAAA,EAAMC,KAAA,EAAW,KAAA,EAAO,oBAAA,EAAsB,IAAI,OAAA;AACtD,CAAA;AAEO,MAAM,kBAA4B,MAAM;AAC7C,EAAA,MAAM,UAAU,SAAA,EAAU;AAC1B,EAAA,MAAM,WAAW,WAAA,EAAY;AAE7B,EAAA,MAAM,gBAAA,GAAmB,CAAC,QAAA,KAAqB;AAE7C,IAAA,QAAA,CAAS,cAAc,CAAA;AAAA,EACzB,CAAA;AAEA,EAAA,uBACE,GAAA,CAAC,IAAA,EAAA,EAAK,OAAA,EAAQ,MAAA,EAAO,SAAA,EAAW,OAAA,CAAQ,IAAA,EACtC,QAAA,kBAAA,GAAA,CAAC,OAAA,EAAA,EAAQ,SAAA,EAAW,OAAA,CAAQ,SAAA,EAC1B,QAAA,kBAAA,GAAA;AAAA,IAAC,IAAA;AAAA,IAAA;AAAA,MACC,SAAA,EAAS,IAAA;AAAA,MACT,OAAA,EAAS,CAAA;AAAA,MACT,KAAA,EAAO,EAAE,MAAA,EAAQ,MAAA,EAAO;AAAA,MACxB,UAAA,EAAW,QAAA;AAAA,MACX,cAAA,EAAe,QAAA;AAAA,MAEf,8BAAC,IAAA,EAAA,EAAK,IAAA,EAAI,MAAC,EAAA,EAAI,EAAA,EAAI,IAAI,CAAA,EAAG,EAAA,EAAI,CAAA,EAC5B,QAAA,kBAAA,GAAA,CAAC,QAAK,SAAA,EAAW,OAAA,CAAQ,YAAY,SAAA,EAAW,CAAA,EAC9C,+BAAC,WAAA,EAAA,EACC,QAAA,EAAA;AAAA,wBAAA,GAAA,CAAC,cAAW,OAAA,EAAQ,IAAA,EAAK,SAAA,EAAW,OAAA,CAAQ,aAAa,QAAA,EAAA,mBAAA,EAEzD,CAAA;AAAA,wBACA,GAAA;AAAA,UAAC,UAAA;AAAA,UAAA;AAAA,YACC,OAAA,EAAQ,OAAA;AAAA,YACR,KAAA,EAAM,eAAA;AAAA,YACN,KAAA,EAAM,QAAA;AAAA,YACN,KAAA,EAAO,EAAE,YAAA,EAAc,EAAA,EAAG;AAAA,YAC3B,QAAA,EAAA;AAAA;AAAA,SAED;AAAA,QACC,aAAA,CAAc,GAAA,CAAI,CAAC,MAAA,KAAW;AAC7B,UAAA,MAAM,gBAAgB,MAAA,CAAO,IAAA;AAC7B,UAAA,uBACE,GAAA;AAAA,YAAC,MAAA;AAAA,YAAA;AAAA,cAEC,OAAA,EAAQ,UAAA;AAAA,cACR,WAAW,OAAA,CAAQ,YAAA;AAAA,cACnB,SAAA,kBAAW,GAAA,CAAC,aAAA,EAAA,EAAc,SAAA,EAAW,QAAQ,UAAA,EAAY,CAAA;AAAA,cACzD,OAAA,EAAS,MAAM,gBAAA,CAAiB,MAAA,CAAO,EAAE,CAAA;AAAA,cACzC,YAAA,EAAY,CAAA,aAAA,EAAgB,MAAA,CAAO,EAAE,CAAA,CAAA;AAAA,cACrC,aAAA,EAAa,CAAA,OAAA,EAAU,MAAA,CAAO,EAAE,CAAA,CAAA;AAAA,cAE/B,QAAA,EAAA,MAAA,CAAO;AAAA,aAAA;AAAA,YARH,MAAA,CAAO;AAAA,WASd;AAAA,QAEJ,CAAC;AAAA,OAAA,EACH,GACF,CAAA,EACF;AAAA;AAAA,KAEJ,CAAA,EACF,CAAA;AAEJ;;;;"}