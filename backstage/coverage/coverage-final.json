{"/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/App.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/App.tsx", "statementMap": {"0": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 15}}, "1": {"start": {"line": 1, "column": 32}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 47}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": null}}, "4": {"start": {"line": 11, "column": 7}, "end": {"line": 11, "column": null}}, "5": {"start": {"line": 12, "column": 49}, "end": {"line": 12, "column": null}}, "6": {"start": {"line": 13, "column": 26}, "end": {"line": 13, "column": null}}, "7": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": null}}, "8": {"start": {"line": 19, "column": 7}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 20, "column": 31}, "end": {"line": 20, "column": null}}, "10": {"start": {"line": 21, "column": 28}, "end": {"line": 21, "column": null}}, "11": {"start": {"line": 22, "column": 33}, "end": {"line": 22, "column": null}}, "12": {"start": {"line": 23, "column": 21}, "end": {"line": 23, "column": null}}, "13": {"start": {"line": 24, "column": 27}, "end": {"line": 24, "column": null}}, "14": {"start": {"line": 25, "column": 27}, "end": {"line": 25, "column": null}}, "15": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": null}}, "16": {"start": {"line": 32, "column": 7}, "end": {"line": 32, "column": null}}, "17": {"start": {"line": 33, "column": 26}, "end": {"line": 33, "column": null}}, "18": {"start": {"line": 34, "column": 38}, "end": {"line": 34, "column": null}}, "19": {"start": {"line": 35, "column": 33}, "end": {"line": 35, "column": null}}, "20": {"start": {"line": 36, "column": 34}, "end": {"line": 36, "column": null}}, "21": {"start": {"line": 37, "column": 46}, "end": {"line": 37, "column": null}}, "22": {"start": {"line": 38, "column": 27}, "end": {"line": 38, "column": null}}, "23": {"start": {"line": 40, "column": 12}, "end": {"line": 62, "column": null}}, "24": {"start": {"line": 43, "column": 4}, "end": {"line": 47, "column": null}}, "25": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": null}}, "26": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": null}}, "27": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 21}, "end": {"line": 58, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1}, "f": {"0": 1}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/apis.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/apis.ts", "statementMap": {"0": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 37}}, "1": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": null}}, "2": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": null}}, "3": {"start": {"line": 12, "column": 37}, "end": {"line": 19, "column": null}}, "4": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 14}}, "loc": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/index.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/index.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoFull.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoFull.tsx", "statementMap": {"0": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 15}}, "1": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 11, "column": null}}, "3": {"start": {"line": 12, "column": 17}, "end": {"line": 27, "column": null}}, "4": {"start": {"line": 13, "column": 18}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 29, "column": 15}, "end": {"line": 29, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 17}, "end": {"line": 12, "column": null}}, "loc": {"start": {"line": 12, "column": 17}, "end": {"line": 27, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 1, "2": 1, "3": 1, "4": 0, "5": 1}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoIcon.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/LogoIcon.tsx", "statementMap": {"0": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 15}}, "1": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 11, "column": null}}, "3": {"start": {"line": 13, "column": 17}, "end": {"line": 28, "column": null}}, "4": {"start": {"line": 14, "column": 18}, "end": {"line": 14, "column": null}}, "5": {"start": {"line": 30, "column": 15}, "end": {"line": 30, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 17}, "end": {"line": 13, "column": null}}, "loc": {"start": {"line": 13, "column": 17}, "end": {"line": 28, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 1, "2": 1, "3": 1, "4": 0, "5": 1}, "f": {"0": 0}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/Root.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/Root.tsx", "statementMap": {"0": {"start": {"line": 60, "column": 13}, "end": {"line": 60, "column": 20}}, "1": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 26}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 5, "column": 25}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 6, "column": 32}, "end": {"line": 6, "column": null}}, "6": {"start": {"line": 7, "column": 26}, "end": {"line": 7, "column": null}}, "7": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": null}}, "8": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": null}}, "9": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "10": {"start": {"line": 14, "column": 35}, "end": {"line": 14, "column": null}}, "11": {"start": {"line": 26, "column": 7}, "end": {"line": 26, "column": null}}, "12": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": null}}, "13": {"start": {"line": 28, "column": 23}, "end": {"line": 28, "column": null}}, "14": {"start": {"line": 29, "column": 36}, "end": {"line": 29, "column": null}}, "15": {"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": null}}, "16": {"start": {"line": 32, "column": 29}, "end": {"line": 45, "column": null}}, "17": {"start": {"line": 47, "column": 20}, "end": {"line": 58, "column": null}}, "18": {"start": {"line": 48, "column": 18}, "end": {"line": 48, "column": null}}, "19": {"start": {"line": 49, "column": 21}, "end": {"line": 49, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 20}, "end": {"line": 47, "column": null}}, "loc": {"start": {"line": 47, "column": 20}, "end": {"line": 58, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 54, "column": 9}, "end": {"line": 54, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 54, "column": 18}, "end": {"line": 54, "column": 33}}, {"start": {"line": 54, "column": 33}, "end": {"line": 54, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 0, "19": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/index.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/Root/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 21}}, "1": {"start": {"line": 1, "column": 21}, "end": {"line": 1, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/catalog/EntityPage.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/catalog/EntityPage.tsx", "statementMap": {"0": {"start": {"line": 399, "column": 13}, "end": {"line": 399, "column": null}}, "1": {"start": {"line": 1, "column": 29}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": null}}, "3": {"start": {"line": 29, "column": 7}, "end": {"line": 29, "column": null}}, "4": {"start": {"line": 35, "column": 7}, "end": {"line": 35, "column": null}}, "5": {"start": {"line": 36, "column": 38}, "end": {"line": 36, "column": null}}, "6": {"start": {"line": 37, "column": 27}, "end": {"line": 37, "column": null}}, "7": {"start": {"line": 41, "column": 7}, "end": {"line": 41, "column": null}}, "8": {"start": {"line": 51, "column": 7}, "end": {"line": 51, "column": null}}, "9": {"start": {"line": 53, "column": 31}, "end": {"line": 53, "column": null}}, "10": {"start": {"line": 54, "column": 28}, "end": {"line": 54, "column": null}}, "11": {"start": {"line": 59, "column": 7}, "end": {"line": 59, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/search/SearchPage.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/packages/app/src/components/search/SearchPage.tsx", "statementMap": {"0": {"start": {"line": 123, "column": 13}, "end": {"line": 123, "column": 26}}, "1": {"start": {"line": 1, "column": 47}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 3, "column": 44}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": null}}, "4": {"start": {"line": 8, "column": 45}, "end": {"line": 8, "column": null}}, "5": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": null}}, "6": {"start": {"line": 17, "column": 7}, "end": {"line": 17, "column": null}}, "7": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": null}}, "8": {"start": {"line": 25, "column": 23}, "end": {"line": 25, "column": null}}, "9": {"start": {"line": 27, "column": 18}, "end": {"line": 40, "column": null}}, "10": {"start": {"line": 27, "column": 48}, "end": {"line": 40, "column": null}}, "11": {"start": {"line": 42, "column": 19}, "end": {"line": 121, "column": null}}, "12": {"start": {"line": 43, "column": 18}, "end": {"line": 43, "column": null}}, "13": {"start": {"line": 44, "column": 20}, "end": {"line": 44, "column": null}}, "14": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": null}}, "15": {"start": {"line": 82, "column": 38}, "end": {"line": 88, "column": null}}, "16": {"start": {"line": 90, "column": 34}, "end": {"line": 90, "column": null}}, "17": {"start": {"line": 90, "column": 54}, "end": {"line": 90, "column": 74}}, "18": {"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": null}}, "19": {"start": {"line": 92, "column": 20}, "end": {"line": 92, "column": null}}, "20": {"start": {"line": 123, "column": 26}, "end": {"line": 123, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 29}, "end": {"line": 27, "column": 30}}, "loc": {"start": {"line": 27, "column": 48}, "end": {"line": 40, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 19}, "end": {"line": 42, "column": null}}, "loc": {"start": {"line": 42, "column": 19}, "end": {"line": 121, "column": null}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": null}}, "loc": {"start": {"line": 80, "column": 26}, "end": {"line": 93, "column": null}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 90, "column": 44}, "end": {"line": 90, "column": 54}}, "loc": {"start": {"line": 90, "column": 54}, "end": {"line": 90, "column": 74}}}}, "branchMap": {"0": {"loc": {"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 15}, "end": {"line": 75, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 0, "11": 1, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0]}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/plugin.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/plugin.ts", "statementMap": {"0": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 28}}, "1": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": null}}, "2": {"start": {"line": 5, "column": 29}, "end": {"line": 5, "column": null}}, "3": {"start": {"line": 6, "column": 34}, "end": {"line": 6, "column": null}}, "4": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": null}}, "5": {"start": {"line": 14, "column": 28}, "end": {"line": 39, "column": null}}, "6": {"start": {"line": 17, "column": 4}, "end": {"line": 37, "column": null}}, "7": {"start": {"line": 25, "column": 32}, "end": {"line": 28, "column": null}}, "8": {"start": {"line": 30, "column": 8}, "end": {"line": 34, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 11}}, "loc": {"start": {"line": 16, "column": 14}, "end": {"line": 38, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 12}}, "loc": {"start": {"line": 24, "column": 58}, "end": {"line": 36, "column": null}}}}, "branchMap": {}, "s": {"0": 2, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 8, "7": 2, "8": 2}, "f": {"0": 8, "1": 2}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/router.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/router.ts", "statementMap": {"0": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 35}}, "1": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "3": {"start": {"line": 4, "column": 20}, "end": {"line": 4, "column": null}}, "4": {"start": {"line": 5, "column": 19}, "end": {"line": 5, "column": null}}, "5": {"start": {"line": 15, "column": 17}, "end": {"line": 15, "column": null}}, "6": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "7": {"start": {"line": 24, "column": 21}, "end": {"line": 27, "column": null}}, "8": {"start": {"line": 29, "column": 2}, "end": {"line": 40, "column": null}}, "9": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": null}}, "10": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": null}}, "11": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": null}}, "12": {"start": {"line": 35, "column": 19}, "end": {"line": 37, "column": null}}, "13": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": null}}, "14": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": null}}, "15": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "16": {"start": {"line": 46, "column": 2}, "end": {"line": 48, "column": null}}, "17": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": null}}, "18": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": null}}}, "fnMap": {"0": {"name": "createRouter", "decl": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 35}}, "loc": {"start": {"line": 14, "column": 1}, "end": {"line": 51, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 31}}, "loc": {"start": {"line": 29, "column": 36}, "end": {"line": 40, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": 30}}, "loc": {"start": {"line": 42, "column": 36}, "end": {"line": 44, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 46, "column": 27}, "end": {"line": 46, "column": 34}}, "loc": {"start": {"line": 46, "column": 39}, "end": {"line": 48, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": null}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 33, "column": null}}]}}, "s": {"0": 4, "1": 2, "2": 2, "3": 2, "4": 2, "5": 4, "6": 4, "7": 4, "8": 4, "9": 4, "10": 4, "11": 0, "12": 4, "13": 3, "14": 4, "15": 2, "16": 4, "17": 1, "18": 4}, "f": {"0": 4, "1": 4, "2": 2, "3": 1}, "b": {"0": [0]}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/createTodoListService.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/createTodoListService.ts", "statementMap": {"0": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 44}}, "1": {"start": {"line": 2, "column": 30}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 4, "column": 19}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "4": {"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": null}}, "5": {"start": {"line": 23, "column": 2}, "end": {"line": 91, "column": null}}, "6": {"start": {"line": 25, "column": 18}, "end": {"line": 25, "column": 29}}, "7": {"start": {"line": 31, "column": 6}, "end": {"line": 58, "column": null}}, "8": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": null}}, "9": {"start": {"line": 42, "column": 8}, "end": {"line": 46, "column": null}}, "10": {"start": {"line": 43, "column": 10}, "end": {"line": 44, "column": null}}, "11": {"start": {"line": 56, "column": 30}, "end": {"line": 56, "column": 70}}, "12": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": null}}, "13": {"start": {"line": 60, "column": 17}, "end": {"line": 60, "column": null}}, "14": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 67}}, "15": {"start": {"line": 62, "column": 22}, "end": {"line": 67, "column": null}}, "16": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "17": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": null}}, "18": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": null}}, "19": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": null}}, "20": {"start": {"line": 85, "column": 19}, "end": {"line": 85, "column": null}}, "21": {"start": {"line": 85, "column": 44}, "end": {"line": 85, "column": 66}}, "22": {"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": null}}, "23": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": null}}, "24": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": null}}}, "fnMap": {"0": {"name": "createTodoListService", "decl": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 44}}, "loc": {"start": {"line": 18, "column": 1}, "end": {"line": 92, "column": null}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 10}}, "loc": {"start": {"line": 24, "column": 35}, "end": {"line": 78, "column": null}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 10}}, "loc": {"start": {"line": 80, "column": 10}, "end": {"line": 82, "column": null}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 10}}, "loc": {"start": {"line": 84, "column": 41}, "end": {"line": 90, "column": null}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 85, "column": 36}, "end": {"line": 85, "column": 44}}, "loc": {"start": {"line": 85, "column": 44}, "end": {"line": 85, "column": 66}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 58, "column": null}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 58, "column": null}}]}, "1": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 46, "column": null}}, "type": "if", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 46, "column": null}}]}, "2": {"loc": {"start": {"line": 56, "column": 30}, "end": {"line": 56, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 56, "column": 30}, "end": {"line": 56, "column": 51}}, {"start": {"line": 56, "column": 55}, "end": {"line": 56, "column": 70}}]}, "3": {"loc": {"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": null}}, "type": "if", "locations": [{"start": {"line": 86, "column": 6}, "end": {"line": 88, "column": null}}]}}, "s": {"0": 2, "1": 1, "2": 1, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 1, "9": 1, "10": 0, "11": 1, "12": 1, "13": 2, "14": 2, "15": 2, "16": 2, "17": 2, "18": 2, "19": 2, "20": 1, "21": 1, "22": 1, "23": 0, "24": 1}, "f": {"0": 2, "1": 2, "2": 2, "3": 1, "4": 1}, "b": {"0": [1], "1": [0], "2": [1, 0], "3": [0]}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/index.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo-backend/src/services/TodoListService/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 38}}, "1": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 2, "1": 1}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/plugin.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/plugin.ts", "statementMap": {"0": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 26}}, "1": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 28}}, "2": {"start": {"line": 4, "column": 7}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 6, "column": 29}, "end": {"line": 6, "column": null}}, "4": {"start": {"line": 8, "column": 28}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 15, "column": 26}, "end": {"line": 21, "column": null}}, "6": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": null}}, "7": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 46}}, "8": {"start": {"line": 19, "column": 56}, "end": {"line": 19, "column": 74}}}, "fnMap": {"0": {"name": "(anonymous_6)", "decl": {"start": {"line": 18, "column": 15}, "end": {"line": 18, "column": null}}, "loc": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": null}}}, "1": {"name": "(anonymous_7)", "decl": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 13}}, "loc": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 46}}}, "2": {"name": "(anonymous_8)", "decl": {"start": {"line": 19, "column": 51}, "end": {"line": 19, "column": 56}}, "loc": {"start": {"line": 19, "column": 56}, "end": {"line": 19, "column": 74}}}}, "branchMap": {}, "s": {"0": 0, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/routes.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/routes.ts", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "1": {"start": {"line": 1, "column": 31}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 3, "column": 28}, "end": {"line": 5, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 2, "1": 1, "2": 1}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleComponent/ExampleComponent.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleComponent/ExampleComponent.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 32}}, "1": {"start": {"line": 1, "column": 33}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": null}}, "3": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1}, "f": {}, "b": {}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/ExampleFetchComponent.tsx": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/ExampleFetchComponent.tsx", "statementMap": {"0": {"start": {"line": 259, "column": 13}, "end": {"line": 259, "column": 26}}, "1": {"start": {"line": 294, "column": 13}, "end": {"line": 294, "column": 37}}, "2": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 28}}, "3": {"start": {"line": 1, "column": 27}, "end": {"line": 1, "column": null}}, "4": {"start": {"line": 7, "column": 7}, "end": {"line": 7, "column": null}}, "5": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": null}}, "6": {"start": {"line": 10, "column": 28}, "end": {"line": 233, "column": null}}, "7": {"start": {"line": 235, "column": 18}, "end": {"line": 241, "column": null}}, "8": {"start": {"line": 259, "column": 26}, "end": {"line": 292, "column": null}}, "9": {"start": {"line": 260, "column": 18}, "end": {"line": 260, "column": null}}, "10": {"start": {"line": 262, "column": 33}, "end": {"line": 267, "column": null}}, "11": {"start": {"line": 269, "column": 15}, "end": {"line": 282, "column": null}}, "12": {"start": {"line": 270, "column": 4}, "end": {"line": 281, "column": null}}, "13": {"start": {"line": 294, "column": 37}, "end": {"line": 308, "column": null}}, "14": {"start": {"line": 296, "column": 36}, "end": {"line": 299, "column": null}}, "15": {"start": {"line": 298, "column": 4}, "end": {"line": 298, "column": null}}, "16": {"start": {"line": 301, "column": 2}, "end": {"line": 305, "column": null}}, "17": {"start": {"line": 302, "column": 4}, "end": {"line": 302, "column": null}}, "18": {"start": {"line": 303, "column": 9}, "end": {"line": 305, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 259, "column": 26}, "end": {"line": 259, "column": 27}}, "loc": {"start": {"line": 259, "column": 53}, "end": {"line": 292, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 269, "column": 25}, "end": {"line": 269, "column": null}}, "loc": {"start": {"line": 269, "column": 25}, "end": {"line": 282, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 294, "column": 37}, "end": {"line": 294, "column": null}}, "loc": {"start": {"line": 294, "column": 37}, "end": {"line": 308, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 296, "column": 45}, "end": {"line": 296, "column": null}}, "loc": {"start": {"line": 296, "column": 45}, "end": {"line": 299, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 301, "column": 2}, "end": {"line": 305, "column": null}}, "type": "if", "locations": [{"start": {"line": 301, "column": 2}, "end": {"line": 305, "column": null}}, {"start": {"line": 303, "column": 9}, "end": {"line": 305, "column": null}}]}, "1": {"loc": {"start": {"line": 303, "column": 9}, "end": {"line": 305, "column": null}}, "type": "if", "locations": [{"start": {"line": 303, "column": 9}, "end": {"line": 305, "column": null}}]}, "2": {"loc": {"start": {"line": 307, "column": 28}, "end": {"line": 307, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 28}, "end": {"line": 307, "column": 37}}, {"start": {"line": 307, "column": 37}, "end": {"line": 307, "column": 39}}]}}, "s": {"0": 0, "1": 3, "2": 0, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 2, "12": 40, "13": 2, "14": 6, "15": 2, "16": 4, "17": 2, "18": 2}, "f": {"0": 2, "1": 40, "2": 6, "3": 2}, "b": {"0": [2, 2], "1": [0], "2": [2, 0]}}, "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/index.ts": {"path": "/home/<USER>/Desktop/this-is-it/backstage/plugins/choreo/src/components/ExampleFetchComponent/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 38}}, "1": {"start": {"line": 1, "column": 38}, "end": {"line": 1, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 2, "1": 1}, "f": {}, "b": {}}}