/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import Dockerode from "dockerode";
import { ContainerRuntimeClientStrategyResult } from "../strategies/types";
export declare const resolveHost: (dockerode: Dockerode, strategyResult: ContainerRuntimeClientStrategyResult, indexServerAddress: string, env?: NodeJS.ProcessEnv) => Promise<string>;
