{"version": 3, "file": "dockerfile-parser.js", "sourceRoot": "", "sources": ["../../src/utils/dockerfile-parser.ts"], "names": [], "mappings": ";;;AAAA,2BAAoC;AACpC,sCAAkD;AAClD,4DAAiD;AAGjD,MAAM,aAAa,GAAG,cAAc,CAAC;AAE9B,KAAK,UAAU,mBAAmB,CAAC,UAAkB,EAAE,SAAoB;IAChF,IAAI;QACF,OAAO,CAAC,MAAM,WAAW,CAAC,UAAU,CAAC,CAAC;aACnC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;aAC5E,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,6BAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACrD;IAAC,OAAO,GAAG,EAAE;QACZ,YAAG,CAAC,KAAK,CAAC,8BAA8B,UAAU,MAAM,GAAG,EAAE,CAAC,CAAC;QAC/D,MAAM,GAAG,CAAC;KACX;AACH,CAAC;AATD,kDASC;AAED,KAAK,UAAU,WAAW,CAAC,UAAkB;IAC3C,OAAO,KAAK,CAAC,IAAI,CACf,CAAC,MAAM,aAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SACpC,KAAK,CAAC,OAAO,CAAC;SACd,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SACvD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,yBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1D,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,EAAU,CAAC;SACzD,MAAM,EAAE,CACZ,CAAC;AACJ,CAAC"}