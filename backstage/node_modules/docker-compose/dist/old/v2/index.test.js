"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var dockerode_1 = __importDefault(require("dockerode"));
var docker = new dockerode_1.default();
// Docker commands, especially builds, can take some time. This makes sure that they can take the time they need.
jest.setTimeout(25000);
// Set to true if you need to diagnose using output
var logOutput = true;
var isContainerRunning = function (name) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, new Promise(function (resolve, reject) {
                docker.listContainers(function (err, containers) {
                    if (err) {
                        reject(err);
                    }
                    var running = (containers || []).filter(function (container) {
                        return container.Names.includes(name);
                    });
                    console.log('running containers', running);
                    resolve(running.length > 0);
                });
            })];
    });
}); };
var repoTags = function (imageInfo) { return imageInfo.RepoTags || []; };
var imageExists = function (name) { return __awaiter(void 0, void 0, void 0, function () {
    var images, foundImage;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, docker.listImages()];
            case 1:
                images = _a.sent();
                foundImage = images.findIndex(function (imageInfo) {
                    return repoTags(imageInfo).includes(name);
                });
                return [2 /*return*/, foundImage > -1];
        }
    });
}); };
var removeImagesStartingWith = function (searchString) { return __awaiter(void 0, void 0, void 0, function () {
    var images, _i, images_1, image, _a, _b, repoTag, dockerImage;
    return __generator(this, function (_c) {
        switch (_c.label) {
            case 0: return [4 /*yield*/, docker.listImages()];
            case 1:
                images = _c.sent();
                _i = 0, images_1 = images;
                _c.label = 2;
            case 2:
                if (!(_i < images_1.length)) return [3 /*break*/, 7];
                image = images_1[_i];
                _a = 0, _b = repoTags(image);
                _c.label = 3;
            case 3:
                if (!(_a < _b.length)) return [3 /*break*/, 6];
                repoTag = _b[_a];
                if (!repoTag.startsWith(searchString)) return [3 /*break*/, 5];
                dockerImage = docker.getImage(repoTag);
                if (logOutput) {
                    process.stdout.write("removing image ".concat(repoTag, " ").concat(dockerImage.id || ''));
                }
                return [4 /*yield*/, dockerImage.remove()];
            case 4:
                _c.sent();
                _c.label = 5;
            case 5:
                _a++;
                return [3 /*break*/, 3];
            case 6:
                _i++;
                return [3 /*break*/, 2];
            case 7: return [2 /*return*/];
        }
    });
}); };
beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        console.log('cleaning up before test');
        return [2 /*return*/, new Promise(function (resolve, reject) {
                docker.listContainers(function (containers) {
                    console.log(containers);
                    return resolve();
                });
                // const childProc = spawn('docker, ["rm", "docker"]')
                // childProc.stdout.on('data', (chunk): void => {
                //   console.log(chunk.toString())
                //   // options.callback?.(chunk, 'stdout')
                // })
                // childProc.stderr.on('data', (chunk): void => {
                //   console.log(chunk.toString())
                //   // options.callback?.(chunk, 'stderr')
                // })
                // childProc.on('exit', (exitCode): void => {
                //   setTimeout(() => {
                //     console.log(`cleanup exitcode: ${exitCode}`)
                //     return resolve()
                //   }, 500)
                // })
            })];
    });
}); });
// describe('ensure container gets started', async (): Promise<void> => {
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure exit code is returned correctly', async (): Promise<void> => {
//   let result = await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   expect(result).toMatchObject({
//     exitCode: 0
//   })
//   result = await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(result).toMatchObject({
//     exitCode: 0
//   })
//   let failedResult = 0
//   try {
//     await compose.logs('non_existent_service', {
//       cwd: path.join(__dirname)
//     })
//   } catch (error: any) {
//     failedResult = error.exitCode
//   }
//   expect(failedResult).toBe(1)
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// describe('starts containers properly with --build and --timeout options', (): void => {
//   beforeEach(
//     async (): Promise<void> => {
//       await compose.down({
//         cwd: path.join(__dirname),
//         log: logOutput,
//         config: 'docker-compose-build.yml'
//       })
//     }
//   )
//   afterEach(
//     async (): Promise<void> => {
//       await compose.down({
//         cwd: path.join(__dirname),
//         log: logOutput,
//         config: 'docker-compose-build.yml'
//       })
//     }
//   )
//   test('ensure container gets started with --build option', async (): Promise<void> => {
//     await compose.upAll({
//       cwd: path.join(__dirname),
//       log: logOutput,
//       config: 'docker-compose-build.yml',
//       commandOptions: ['--build']
//     })
//     expect(await isContainerRunning('/compose_test_nginx')).toBeTruthy()
//   })
//   test('ensure container gets started with --build and --timeout option', async (): Promise<void> => {
//     await compose.upAll({
//       cwd: path.join(__dirname),
//       log: logOutput,
//       config: 'docker-compose-build.yml',
//       commandOptions: [['--build'], ['--timeout', '5']]
//     })
//     expect(await isContainerRunning('/compose_test_nginx')).toBeTruthy()
//   })
//   test('ensure container gets started with --build and --timeout option with different command style', async (): Promise<void> => {
//     await compose.upAll({
//       cwd: path.join(__dirname),
//       log: logOutput,
//       config: 'docker-compose-build.yml',
//       commandOptions: ['--build', ['--timeout', '5']]
//     })
//     expect(await isContainerRunning('/compose_test_nginx')).toBeTruthy()
//   })
// })
// test('ensure container command executed with --workdir command option', async (): Promise<void> => {
//   await compose.down({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-42.yml'
//   })
//   const result = await compose.run('some-service', 'pwd', {
//     cwd: path.join(__dirname),
//     log: true,
//     config: 'docker-compose-42.yml',
//     composeOptions: ['--verbose'],
//     // Alpine has "/" as default
//     commandOptions: ['--workdir', '/home/<USER>']
//   })
//   expect(result.out).toBe('/home/<USER>')
//   await compose.down({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-42.yml'
//   })
// })
// test('ensure only single container gets started', async (): Promise<void> => {
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   await compose.upOne('web', { cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure only multiple containers get started', async (): Promise<void> => {
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   await compose.upMany(['web'], { cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure container gets down', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
// })
// test('ensure container gets stopped', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.stop({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure only single container gets stopped', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.stopOne('proxy', { cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure multiple containers gets stopped', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.stopMany(
//     { cwd: path.join(__dirname), log: logOutput },
//     'proxy',
//     'web'
//   )
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure only single container gets paused then resumed', async (): Promise<void> => {
//   const opts = { cwd: path.join(__dirname), log: logOutput }
//   await compose.upAll(opts)
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.pauseOne('proxy', opts)
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   let errMsg
//   try {
//     await compose.exec('proxy', 'cat /etc/os-release', opts)
//   } catch (err: any) {
//     errMsg = err.err
//   }
//   expect(errMsg).toContain('is paused')
//   await compose.unpauseOne('proxy', opts)
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   const std = await compose.exec('proxy', 'cat /etc/os-release', opts)
//   expect(std.out).toContain('Alpine Linux')
//   await compose.down(opts)
// })
// test('ensure container gets started with --abort-on-container-exit option', async (): Promise<void> => {
//   const result = await compose.upAll({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     commandOptions: ['--abort-on-container-exit']
//   })
//   expect(result).toMatchObject({
//     exitCode: 0
//   })
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure container gets started with --abort-on-container-exit option correctly aborts all services when a container exits', async (): Promise<void> => {
//   const result = await compose.upAll({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     commandOptions: ['--abort-on-container-exit']
//   })
//   expect(result.out).toMatch(/Aborting on container exit/)
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure container gets killed', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   console.log('up')
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.kill({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure custom ymls are working', async (): Promise<void> => {
//   const config = './docker-compose-2.yml'
//   const cwd = path.join(__dirname)
//   await compose.upAll({ cwd, log: logOutput, config })
//   expect(await isContainerRunning('/compose_test_web_2')).toBeTruthy()
//   // config & [config] are the same thing, ensures that multiple configs are handled properly
//   await compose.kill({ cwd, log: logOutput, config: config })
//   expect(await isContainerRunning('/compose_test_web_2')).toBeFalsy()
//   await compose.down({ cwd, log: logOutput, config })
// })
// test('ensure run and exec are working', async (): Promise<void> => {
//   const checkOSID = (out, id): void => {
//     // parse /etc/os-release contents
//     const re = /([\w,_]+)=(.*)/g
//     let match
//     const os: { ID?: string } = {}
//     while ((match = re.exec(out)) !== null) {
//       // eslint-disable-line no-cond-assign
//       os[match[1]] = match[2]
//     }
//     expect(os.ID).toBe(id)
//   }
//   const opts = { cwd: path.join(__dirname), log: logOutput }
//   await compose.upAll(opts)
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   let std = await compose.exec('web', 'cat /etc/os-release', opts)
//   checkOSID(std.out, 'debian')
//   std = await compose.run('proxy', 'cat /etc/os-release', opts)
//   checkOSID(std.out, 'alpine')
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('ensure run and exec with command defined as array are working', async (): Promise<void> => {
//   const checkOSID = (out, id): void => {
//     // parse /etc/os-release contents
//     const re = /([\w,_]+)=(.*)/g
//     let match
//     const os: { ID?: string } = {}
//     while ((match = re.exec(out)) !== null) {
//       // eslint-disable-line no-cond-assign
//       os[match[1]] = match[2]
//     }
//     expect(os.ID).toBe(id)
//   }
//   const opts = { cwd: path.join(__dirname), log: false }
//   await compose.upAll(opts)
//   expect(await isContainerRunning('/compose_test_web')).toBe(true)
//   let std = await compose.exec(
//     'web',
//     ['/bin/sh', '-c', 'cat /etc/os-release'],
//     opts
//   )
//   checkOSID(std.out, 'debian')
//   std = await compose.run(
//     'proxy',
//     ['/bin/sh', '-c', 'cat /etc/os-release'],
//     opts
//   )
//   checkOSID(std.out, 'alpine')
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('build accepts config as string', async (): Promise<void> => {
//   const configuration = await new Promise<string>(function (
//     resolve,
//     reject
//   ): void {
//     readFile(
//       path.join(__dirname, 'docker-compose-2.yml'),
//       function (err, content) {
//         if (err) {
//           reject(err)
//           return
//         }
//         resolve(content.toString())
//       }
//     )
//   })
//   const config = {
//     configAsString: configuration,
//     log: logOutput
//   }
//   await compose.upAll(config)
//   const result = await compose.port('web', 8888, config)
//   expect(result.data.address).toBe('0.0.0.0')
//   expect(result.data.port).toBe(8888)
//   await compose.down(config)
// })
// test('build single service', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-build.yml'
//   }
//   await removeImagesStartingWith('compose-test-build-image')
//   await compose.buildOne('build_test_1', opts)
//   expect(await imageExists('compose-test-build-image-1:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-2:test')).toBeFalsy()
//   expect(await imageExists('compose-test-build-image-3:test')).toBeFalsy()
//   expect(await imageExists('compose-test-build-image-4:test')).toBeFalsy()
//   await removeImagesStartingWith('compose-test-build-image')
// })
// test('build multiple services', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-build.yml'
//   }
//   await compose.buildMany(['build_test_2', 'build_test_3'], opts)
//   expect(await imageExists('compose-test-build-image-1:test')).toBeFalsy()
//   expect(await imageExists('compose-test-build-image-2:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-3:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-4:test')).toBeFalsy()
//   await removeImagesStartingWith('compose-test-build-image')
// })
// test('build all services', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-build.yml'
//   }
//   await compose.buildAll(opts)
//   expect(await imageExists('compose-test-build-image-1:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-2:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-3:test')).toBeTruthy()
//   expect(await imageExists('compose-test-build-image-4:test')).toBeTruthy()
//   await removeImagesStartingWith('compose-test-build-image')
// })
// test('pull single service', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose.yml'
//   }
//   await removeImagesStartingWith('nginx:1.19.9-alpine')
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeFalsy()
//   await compose.pullOne('proxy', opts)
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeTruthy()
// })
// test('pull multiple services', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose.yml'
//   }
//   await removeImagesStartingWith('nginx:1.16.0')
//   await removeImagesStartingWith('nginx:1.19.9-alpine')
//   expect(await imageExists('nginx:1.16.0')).toBeFalsy()
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeFalsy()
//   await compose.pullMany(['web', 'proxy'], opts)
//   expect(await imageExists('nginx:1.16.0')).toBeTruthy()
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeTruthy()
// })
// test('pull all services', async (): Promise<void> => {
//   const opts = {
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose.yml'
//   }
//   await removeImagesStartingWith('nginx:1.16.0')
//   await removeImagesStartingWith('nginx:1.19.9-alpine')
//   expect(await imageExists('nginx:1.16.0')).toBeFalsy()
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeFalsy()
//   await compose.pullAll(opts)
//   expect(await imageExists('nginx:1.16.0')).toBeTruthy()
//   expect(await imageExists('nginx:1.19.9-alpine')).toBeTruthy()
// })
// afterEach(
//   async (): Promise<void> => {
//     interface Container {
//       Names: string[]
//       Id: string
//     }
//     docker.listContainers((err, containers: Container[]): void => {
//       if (err) {
//         throw err
//       }
//       containers.forEach((container): void => {
//         console.log(`stopping ${container.Id} ${container.Names}`)
//         docker.getContainer(container.Id).stop()
//         docker.getContainer(container.Id).remove()
//       })
//     })
//     await removeImagesStartingWith('compose-test-build-image')
//   }
// )
// test('config show data for docker-compose files', async (): Promise<void> => {
//   const std = await compose.config({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-42.yml'
//   })
//   // expect(std.data.config.version).toBe('3')
//   expect(std.data.config.services['some-service']['image']).toBe(
//     'nginx:1.19.9-alpine'
//   )
//   expect(std.data.config.volumes['db-data']).toEqual({ name: 'v2_db-data' })
// })
// test('config show data for docker-compose files (services)', async (): Promise<void> => {
//   const std = await compose.configServices({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-build.yml'
//   })
//   expect(std.data.services.length).toBe(5)
//   expect(std.data.services[0]).toContain('build-nginx')
//   expect(std.err).toBeFalsy()
// })
// test('config show data for docker-compose files (volumes)', async (): Promise<void> => {
//   const std = await compose.configVolumes({
//     cwd: path.join(__dirname),
//     log: logOutput,
//     config: 'docker-compose-42.yml'
//   })
//   expect(std.data.volumes.length).toBe(1)
//   expect(std.data.volumes[0]).toContain('db-data')
//   expect(std.err).toBeFalsy()
// })
// test('ps shows status data for started containers', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   const std = await compose.ps({ cwd: path.join(__dirname), log: logOutput })
//   expect(std.err).toBeFalsy()
//   expect(std.data.services.length).toBe(3)
//   const web = std.data.services.find(
//     (service) => service.name === 'compose_test_web'
//   )
//   expect(std.data.services.length).toBe(3)
//   expect(web?.ports.length).toBe(2)
//   expect(web?.ports[0].exposed.port).toBe(443)
//   expect(web?.ports[0].exposed.protocol).toBe('tcp')
//   expect(web?.ports[0].mapped?.port).toBe(443)
//   expect(web?.ports[0].mapped?.address).toBe('0.0.0.0')
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test.only('ps does not show status data for stopped containers', async (): Promise<void> => {
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
//   jest.setTimeout(500)
//   await compose.upOne('web', { cwd: path.join(__dirname), log: logOutput })
//   jest.setTimeout(500)
//   const std = await compose.ps({ cwd: path.join(__dirname), log: logOutput })
//   console.log('data', std.data.services)
//   expect(std.err).toBeFalsy()
//   const web = std.data.services.find(
//     (service) => service.name === 'compose_test_web'
//   )
//   const proxy = std.data.services.find(
//     (service) => service.name === 'compose_test_proxy'
//   )
//   expect(web?.name).toBe('compose_test_web')
//   expect(proxy).toBeFalsy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('restartAll does restart all containers', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   await compose.restartAll({ cwd: path.join(__dirname), log: logOutput })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('restartMany does restart selected containers', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   await compose.restartMany(['web', 'proxy'], {
//     cwd: path.join(__dirname),
//     log: logOutput
//   })
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('restartOne does restart container', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   await compose.restartOne('proxy', {
//     cwd: path.join(__dirname),
//     log: logOutput
//   })
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('logs does follow service logs', async (): Promise<void> => {
//   await compose.upAll({ cwd: path.join(__dirname), log: logOutput })
//   const std = await compose.logs('proxy', {
//     cwd: path.join(__dirname),
//     log: logOutput
//   })
//   expect(std.out.includes('compose_test_proxy')).toBeTruthy()
//   await compose.down({ cwd: path.join(__dirname), log: logOutput })
// })
// test('returns the port for a started service', async (): Promise<void> => {
//   const config = {
//     cwd: path.join(__dirname),
//     config: './docker-compose-2.yml',
//     log: logOutput
//   }
//   await compose.upAll(config)
//   const port = await compose.port('web', 8888, config)
//   expect(port.out).toMatch(/.*:[0-9]{1,5}/)
//   await compose.down(config)
// })
// test('removes container', async (): Promise<void> => {
//   const config = {
//     cwd: path.join(__dirname),
//     config: './docker-compose.yml',
//     log: logOutput
//   }
//   await compose.upAll(config)
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeTruthy()
//   await compose.rm({ ...config, commandOptions: ['-s'] }, 'proxy')
//   expect(await isContainerRunning('/compose_test_web')).toBeTruthy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
//   await compose.rm({ ...config, commandOptions: ['-s'] }, 'proxy', 'web')
//   expect(await isContainerRunning('/compose_test_web')).toBeFalsy()
//   expect(await isContainerRunning('/compose_test_proxy')).toBeFalsy()
// })
// test('returns version information', async (): Promise<void> => {
//   const version = (await compose.version()).data.version
//   expect(version).toMatch(/^(\d+\.)?(\d+\.)?(\*|\d+)$/)
// })
// test('parse ps output', () => {
//   const output = `       Name                     Command               State                     Ports                  \n-------------------------------------------------------------------------------------------------------\ncompose_test_hello   /hello                           Exit 0                                           \ncompose_test_proxy   /docker-entrypoint.sh ngin ...   Up       80/tcp                                  \ncompose_test_web     nginx -g daemon off;             Up       0.0.0.0:443->443/tcp, 0.0.0.0:80->80/tcp\n`
//   const psOut = mapPsOutput(output)
//   expect(psOut.services[0]).toEqual({
//     command: '/hello',
//     name: 'compose_test_hello',
//     state: 'Exit 0',
//     ports: []
//   })
//   expect(psOut.services[1]).toEqual({
//     command: '/docker-entrypoint.sh ngin ...',
//     name: 'compose_test_proxy',
//     state: 'Up',
//     ports: [{ exposed: { port: 80, protocol: 'tcp' } }]
//   })
//   expect(psOut.services[2]).toEqual({
//     command: 'nginx -g daemon off;',
//     name: 'compose_test_web',
//     state: 'Up',
//     ports: [
//       {
//         exposed: { port: 443, protocol: 'tcp' },
//         mapped: { port: 443, address: '0.0.0.0' }
//       },
//       {
//         exposed: { port: 80, protocol: 'tcp' },
//         mapped: { port: 80, address: '0.0.0.0' }
//       }
//     ]
//   })
// })
// test('ps returns container ids when quiet', () => {
//   const output = `64848fc721dfeff435edc7d4bb42e2f0e0a10d0c7602b73729a7fd7b09b7586f
// aed60ce17575e69c56cc4cb07eeba89b5d7b7b2b307c8b87f3363db6af850719
// f49548fa0b1f88846b78c65c6ea7f802bcbdfb2cf10204497eb89ba622d7715b
// `
//   const psOut = mapPsOutput(output, { commandOptions: ['-q'] })
//   expect(psOut.services[0]).toEqual(
//     expect.objectContaining({
//       name: '64848fc721dfeff435edc7d4bb42e2f0e0a10d0c7602b73729a7fd7b09b7586f'
//     })
//   )
//   expect(psOut.services[1]).toEqual(
//     expect.objectContaining({
//       name: 'aed60ce17575e69c56cc4cb07eeba89b5d7b7b2b307c8b87f3363db6af850719'
//     })
//   )
//   expect(psOut.services[2]).toEqual(
//     expect.objectContaining({
//       name: 'f49548fa0b1f88846b78c65c6ea7f802bcbdfb2cf10204497eb89ba622d7715b'
//     })
//   )
// })
// test('ps returns container names when --services is passed in options', () => {
//   const output = `web
// proxy
// hello
// `
//   const psOut = mapPsOutput(output, { commandOptions: ['--services'] })
//   expect(psOut.services[0]).toEqual(
//     expect.objectContaining({
//       name: 'web'
//     })
//   )
//   expect(psOut.services[1]).toEqual(
//     expect.objectContaining({
//       name: 'proxy'
//     })
//   )
//   expect(psOut.services[2]).toEqual(
//     expect.objectContaining({
//       name: 'hello'
//     })
//   )
// })
// test('ensure progress callback is called', async (): Promise<void> => {
//   const config = {
//     cwd: path.join(__dirname),
//     config: './docker-compose.yml',
//     callback: jest.fn()
//   }
//   await compose.upAll(config)
//   expect(config.callback).toBeCalled()
//   await compose.down(config)
// })
