{"version": 3, "file": "configuration-strategy.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/strategies/configuration-strategy.ts"], "names": [], "mappings": ";;;;;;AACA,2DAA6B;AAC7B,gDAAwB;AACxB,6BAA0B;AAG1B,2CAA2D;AAE3D,MAAa,qBAAqB;IACxB,UAAU,CAAU;IACpB,eAAe,CAAqB;IACpC,cAAc,CAAqB;IAE3C,OAAO;QACL,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,SAAS;QACb,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,MAAM,IAAA,kCAAyB,GAAE,CAAC;QAE1F,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QAErC,MAAM,aAAa,GAAkB,EAAE,CAAC;QAExC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,QAAQ,KAAK,EAAE,EAAE;YACnB,aAAa,CAAC,IAAI,GAAG,QAAQ,CAAC;YAC9B,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;SAC3B;aAAM;YACL,aAAa,CAAC,UAAU,GAAG,QAAQ,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrE,aAAa,CAAC,EAAE,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;YAClF,aAAa,CAAC,IAAI,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC;YACtF,aAAa,CAAC,GAAG,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;SACrF;QAED,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,UAAU;YACpB,aAAa;YACb,kBAAkB,EAAE;gBAClB,WAAW,EAAE,IAAI,CAAC,UAAU;gBAC5B,iBAAiB,EAAE,IAAI,CAAC,eAAe;gBACvC,gBAAgB,EAAE,IAAI,CAAC,cAAc;aACtC;YACD,kBAAkB,EAAE,IAAI;SACzB,CAAC;IACJ,CAAC;CACF;AA/CD,sDA+CC"}