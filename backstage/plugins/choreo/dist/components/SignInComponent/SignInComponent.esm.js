import { jsx, jsxs } from 'react/jsx-runtime';
import { useNavigate } from 'react-router-dom';
import { makeStyles, Grid, Card, CardContent, Typography, Button } from '@material-ui/core';
import { Page, Content } from '@backstage/core-components';
import { AccountCircle, GitHub, Business, Domain, Email } from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
  root: {
    minHeight: "100vh",
    backgroundColor: theme.palette.background.default
  },
  container: {
    height: "100vh",
    padding: theme.spacing(4)
  },
  signInCard: {
    padding: theme.spacing(3),
    height: "fit-content",
    maxWidth: 400,
    margin: "0 auto"
  },
  signInTitle: {
    marginBottom: theme.spacing(3),
    textAlign: "center",
    color: theme.palette.text.primary
  },
  signInButton: {
    width: "100%",
    marginBottom: theme.spacing(2),
    justifyContent: "flex-start",
    textTransform: "none",
    padding: theme.spacing(1.5, 2),
    "&:hover": {
      backgroundColor: theme.palette.action.hover
    }
  },
  buttonIcon: {
    marginRight: theme.spacing(2)
  }
}));
const signInOptions = [
  { icon: AccountCircle, label: "Continue with Google", id: "google" },
  { icon: GitHub, label: "Continue with GitHub", id: "github" },
  { icon: Business, label: "Continue with Microsoft", id: "microsoft" },
  { icon: Domain, label: "Sign in with Enterprise ID", id: "enterprise" },
  { icon: Email, label: "Sign in with Email", id: "email" }
];
const SignInComponent = () => {
  const classes = useStyles();
  const navigate = useNavigate();
  const handleNavigation = (optionId) => {
    navigate("/choreo-home");
  };
  return /* @__PURE__ */ jsx(Page, { themeId: "home", className: classes.root, children: /* @__PURE__ */ jsx(Content, { className: classes.container, children: /* @__PURE__ */ jsx(
    Grid,
    {
      container: true,
      spacing: 4,
      style: { height: "100%" },
      alignItems: "center",
      justifyContent: "center",
      children: /* @__PURE__ */ jsx(Grid, { item: true, xs: 12, md: 6, lg: 4, children: /* @__PURE__ */ jsx(Card, { className: classes.signInCard, elevation: 2, children: /* @__PURE__ */ jsxs(CardContent, { children: [
        /* @__PURE__ */ jsx(Typography, { variant: "h4", className: classes.signInTitle, children: "Sign In to Choreo" }),
        /* @__PURE__ */ jsx(
          Typography,
          {
            variant: "body2",
            color: "textSecondary",
            align: "center",
            style: { marginBottom: 24 },
            children: "Choose your preferred sign-in method"
          }
        ),
        signInOptions.map((option) => {
          const IconComponent = option.icon;
          return /* @__PURE__ */ jsx(
            Button,
            {
              variant: "outlined",
              className: classes.signInButton,
              startIcon: /* @__PURE__ */ jsx(IconComponent, { className: classes.buttonIcon }),
              onClick: () => handleNavigation(option.id),
              "aria-label": `Sign in with ${option.id}`,
              "data-testid": `signin-${option.id}`,
              children: option.label
            },
            option.id
          );
        })
      ] }) }) })
    }
  ) }) });
};

export { SignInComponent };
//# sourceMappingURL=SignInComponent.esm.js.map
