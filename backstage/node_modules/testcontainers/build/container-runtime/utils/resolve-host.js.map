{"version": 3, "file": "resolve-host.js", "sourceRoot": "", "sources": ["../../../src/container-runtime/utils/resolve-host.ts"], "names": [], "mappings": ";;;AACA,2BAAgC;AAChC,6BAA0B;AAC1B,yCAAmC;AAEnC,yDAAoD;AAE7C,MAAM,WAAW,GAAG,KAAK,EAC9B,SAAoB,EACpB,cAAoD,EACpD,kBAA0B,EAC1B,MAAyB,OAAO,CAAC,GAAG,EACnB,EAAE;IACnB,IAAI,cAAc,CAAC,kBAAkB,EAAE;QACrC,IAAI,GAAG,CAAC,4BAA4B,KAAK,SAAS,EAAE;YAClD,OAAO,GAAG,CAAC,4BAA4B,CAAC;SACzC;KACF;IAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,SAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IAE3D,QAAQ,QAAQ,EAAE;QAChB,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ,CAAC;QACd,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC;QAClB,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ,CAAC,CAAC;YACb,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;gBACrF,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC1D,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,OAAO,OAAO,CAAC;iBAChB;gBACD,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;gBAC/E,IAAI,cAAc,KAAK,SAAS,EAAE;oBAChC,OAAO,cAAc,CAAC;iBACvB;aACF;YACD,OAAO,WAAW,CAAC;SACpB;QACD;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;KACxD;AACH,CAAC,CAAC;AArCW,QAAA,WAAW,eAqCtB;AAEF,MAAM,WAAW,GAAG,KAAK,EAAE,SAAoB,EAAE,WAAmB,EAA+B,EAAE;IACnG,YAAG,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACjD,MAAM,aAAa,GAAuB,MAAM,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;IAC5F,OAAO,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,EAAE,OAAO,CAAC;AAC9F,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,KAAK,EAAE,SAAoB,EAAE,kBAA0B,EAA+B,EAAE;IACjH,YAAG,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACzD,OAAO,IAAA,iCAAc,EAAC,SAAS,EAAE,kBAAkB,EAAE,aAAa,EAAE;QAClE,IAAI;QACJ,IAAI;QACJ,uCAAuC;KACxC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,IAAA,eAAU,EAAC,aAAa,CAAC,CAAC"}